<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.salary.dao.SalaryDao">

    <select id="getMarketingOnlineUPayMethod" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select id as value,
        payName as name
        from dbo.upayConfig u1
        with (nolock)
        where not exists (select 1 from dbo.upayConfig u2 with(nolock) where u2.payName = u1.payName and u1.id &lt; u2.id);
    </select>
    <select id="getMarketingOnlineAliPayMethod" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select id as value,
        payName as name
        from dbo.alipayConfig u1
        with (nolock)
        where not exists (select 1 from dbo.alipayConfig u2 with (nolock) where u2.payName = u1.payName and u1.id &lt; u2.id);
    </select>
    <select id="getMarketingOnlineWeiXinPayMethod" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select id as value,
        payName as name
        from dbo.weixinConfig u1
        with (nolock)
        where not exists (select 1 from dbo.weixinConfig u2 with (nolock) where u2.payName = u1.payName and u1.id &lt; u2.id);
    </select>
    <select id="getMarketingOnlineSwiftPassMethod" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select id as value,
        payName as name
        from dbo.swiftpassConfig u1
        with (nolock)
        where not exists (select 1 from dbo.swiftpassConfig u2 with (nolock) where u2.payName = u1.payName and u1.id &lt; u2.id);
    </select>
    <select id="getOperatorKind" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        select ID as value, Name as name
        from category with (nolock)
        where id in (select id from f_category_children(3)) and IsShow=1 and isVirtualGoods=1
    </select>
    <select id="getOrderDeliveryList" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO">
        seLect id as value , name_ as name
        from delivery with (nolock)
        order by rank_
    </select>

    <select id="listSaleOrder" resultType="com.jiuji.oa.salary.SaleOrder">
        SELECT *,t.count * t.price saleAmount,t.count * t.profit trueProfit,
        t.count * t.profit1 trueProfit1,
        t.count * t.profit2 trueProfit2,
        t.count * t.profit3 trueProfit3
        ,CASE WHEN  t.profit &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation
        <if test="isOpenCheck!=null and isOpenCheck>0">
            ,t.count * t.checkProfit_ checkProfit
            ,CASE WHEN  t.checkProfit_ &lt; 0 THEN 1 ELSE 0 END checkSalaryLossRelation
        </if>
        FROM
        (
        <include refid="sale_order_columns_1"></include>
        1 userType,
        0 refunded
        <include refid="sale_order_columns_1_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.seller
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check in (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_1"></include>
        1 userType,
        1 refunded
        <include refid="sale_order_columns_1_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.seller
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_1"></include>
        2 userType,
        0 refunded
        <include refid="sale_order_columns_1_from"></include>
        LEFT JOIN dbo.ch999_user u2 WITH(NOLOCK) ON u2.ch999_name = s.trader
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u2.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_1"></include>
        2 userType,
        1 refunded
        <include refid="sale_order_columns_1_from"></include>
        LEFT JOIN dbo.ch999_user u2 WITH(NOLOCK) ON u2.ch999_name = s.trader
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u2.ch999_id = #{ch999Id}

        <if test="mkcIds!=null and mkcIds.size>0">
            UNION ALL
            <include refid="sale_order_columns_2"></include>
            LEFT JOIN dbo.displayProductInfo d with(nolock) ON d.basket_id = b.basket_id
            WHERE ISNULL(b.isdel,0) = 0 AND b.type = 22 AND s.sub_check in (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
            AND b.ismobile = 1 AND k.id IN
            <foreach collection="mkcIds" separator="," item="mkcId" open="(" close=")">
                #{mkcId}
            </foreach>
        </if>
        <if test="oldMkcIds!=null and oldMkcIds.size>0">
            UNION ALL
            <include refid="sale_order_columns_2"></include>
            left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
            LEFT JOIN dbo.displayProductInfo d with(nolock) ON d.basket_id = b.basket_id
            WHERE ISNULL(b.isdel,0) = 0 AND b.type = 22 AND s.sub_check in (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
            AND  b.ismobile = 0 AND d.id IN
            <foreach collection="oldMkcIds" separator="," item="oldMkcId" open="(" close=")">
                #{oldMkcId}
            </foreach>
        </if>
        <if test="mkcIds!=null and mkcIds.size>0">
            UNION ALL
            SELECT 'marketing' orderKind,
            CASE WHEN b.type = 22 THEN '1-3' ELSE '1-1' END orderType,
            s.sub_id subId,
            s.areaid areaId,
            k.id mkcId,
            p.cid,
            p.productid productId,
            s.subtype orderVType,
            s.sub_pay orderPayType,
            s.delivery orderDelivery,
            b.basket_id basketId,
            p.brandID brandId,
            b.ppriceid ppId,
            b.ismobile salaryProductType,
            (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
            CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
            case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
            when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
            when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
            else b.price2 - isnull(b.inprice,b.price2) end profit,
            CASE
            WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
            ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
            CASE
            WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
            ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
            CASE
            WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
            ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
            <if test="isOpenCheck!=null and isOpenCheck>0">
                b.price2 - (CASE WHENb.price2 - (CASE WHEN b.ismobile = 1 AND isnull(b.examPrice,k.examPrice) > 0 then isnull(b.examPrice,k.examPrice)
                WHEN b.ismobile = 1 then isnull(k.staticPrice,price2)
                ELSE isnull(b.examPrice,isnull(b.inprice,price2)) END) checkProfit_,
            </if>
            22 orderDetailType,
            p.pLabel salaryProductLabel,
            s.returnDate refundDate,
            s.tradeDate,
            CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
            CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
            3 userType,
            0 refunded
            FROM dbo.sub s WITH(NOLOCK)
            INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
            LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
            left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
            LEFT JOIN dbo.displayProductInfo d with(nolock) ON d.basket_id = b.basket_id
            left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
            WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
            AND b.ismobile = 1 AND k.id IN
            <foreach collection="oldMkcIds" separator="," item="oldMkcId" open="(" close=")">
                #{oldMkcId}
            </foreach>
        </if>
        <if test="oldMkcIds!=null and oldMkcIds.size>0">
            UNION ALL
            <include refid="sale_order_columns_2"></include>
            left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
            LEFT JOIN dbo.displayProductInfo d with(nolock) ON d.basket_id = b.basket_id
            WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
            AND b.ismobile = 0 AND d.id IN
            <foreach collection="oldMkcIds" separator="," item="oldMkcId" open="(" close=")">
                #{oldMkcId}
            </foreach>
        </if>

        UNION ALL

        SELECT 'marketing' orderKind,
        '1-2' orderType,
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        p.cid,
        p.productid productId,
        <choose>
            <when test="isJiuJi">
                case when isnull(b.isOnShop, 0)=1 and isnull(s.subtype, 0) in (2,3,4,5,6,7,11,16,17,18,19,22)  then 1 else s.subtype end orderVType,
            </when>
            <otherwise>
                s.subtype orderVType,
            </otherwise>
        </choose>
        s.delivery orderDelivery,
        b.basket_id basketId,
        p.brandID brandId,
        b.ppriceid ppId,
        b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        ISNULL(b.price2,b.price) - k.inprice - ISNULL(k.addprice,0) profit,
        null profit1,
        null profit2,
        null profit3,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            NULL checkProfit_,
        </if>
        -1 orderDetailType,
        p.pLabel salaryProductLabel,
        s.returnDate refundDate,
        s.tradeDate1,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        1 userType,
        0 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.seller
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_3"></include>
        2 userType,
        0 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = s.trader
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_3"></include>
        3 userType,
        0 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = k.sjUser
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_3"></include>
        1 userType,
        1 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.seller
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_3"></include>
        2 userType,
        1 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = s.trader
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="sale_order_columns_3"></include>
        3 userType,
        1 refunded
        <include refid="sale_order_columns_3_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = k.sjUser
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        union ALL
        <include refid="sale_order_columns_3"></include>
        4 userType,
        0 refunded
        <include refid="sale_order_columns_3_from"></include>
        left join dbo.recover_basket rb WITH(NOLOCK) on k.from_basket_id=rb.id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = k.checkCommentUser
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND  k.issalf=1 and s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        union all

        <include refid="sale_order_columns_3"></include>
        4 userType,
        1 refunded
        <include refid="sale_order_columns_3_from"></include>
        left join dbo.recover_basket rb WITH(NOLOCK) on k.from_basket_id=rb.id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = K.checkCommentUser
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 and k.issalf=1 AND s.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        ) t
    </select>
    <sql id="sale_order_columns_1">
        SELECT 'marketing' orderKind,
        CASE WHEN b.type = 22 THEN '1-3' ELSE '1-1' END orderType,
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        p.cid,
        p.productid productId,
        s.subtype orderVType,
        s.delivery orderDelivery,
        b.basket_id basketId,
        p.brandID brandId,
        b.ppriceid ppId,
        b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        CASE WHEN ISNULL(b.ismobile,0)=0 THEN (b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0))-b.inprice
        WHEN ISNULL(b.ismobile,0)=1 AND z.mkc_id IS NOT NULL AND b.price &lt; k.staticPrice THEN 0.0
        WHEN ISNULL(b.ismobile,0)=1 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.staticPrice ELSE b.price-b.inprice END profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            CASE WHEN ISNULL(b.examPrice,0)>0 AND ISNULL(b.ismobile,0)=0 THEN b.price-isnull(b.youhuiPrice,0)-isnull(jifenPrice,0)- b.examPrice
            WHEN ISNULL(k.examPrice,0)>0 AND ISNULL(b.ismobile,0)=1 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.examPrice
            WHEN ISNULL(b.ismobile,0)=0 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)- b.inprice
            WHEN z.mkc_id IS NOT NULL AND (b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)) &lt; k.staticPrice THEN 0.0 ELSE b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.staticPrice END checkProfit_,
        </if>
        b.type  orderDetailType,
        p.pLabel salaryProductLabel,
        s.returnDate refundDate,
        s.tradeDate1 tradeDate,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
    </sql>
    <sql id="sale_order_columns_1_from">
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
    </sql>
    <sql id="sale_order_columns_2">
        SELECT 'marketing' orderKind,
        CASE WHEN b.type = 22 THEN '1-3' ELSE '1-1' END orderType,
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        p.cid,
        p.productid productId,
        s.subtype orderVType,
        s.sub_pay orderPayType,
        s.delivery orderDelivery,
        b.basket_id basketId,
        p.brandID brandId,
        b.ppriceid ppId,
        b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        CASE WHEN ISNULL(b.ismobile,0)=0 THEN (b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0))-b.inprice
        WHEN ISNULL(b.ismobile,0)=1 AND z.mkc_id IS NOT NULL AND b.price &lt; k.staticPrice THEN 0.0
        WHEN ISNULL(b.ismobile,0)=1 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.staticPrice ELSE b.price-b.inprice END profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            CASE WHEN ISNULL(b.examPrice,0)>0 AND ISNULL(b.ismobile,0)=0 THEN b.price-isnull(b.youhuiPrice,0)-isnull(jifenPrice,0)- b.examPrice
            WHEN ISNULL(k.examPrice,0)>0 AND ISNULL(b.ismobile,0)=1 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.examPrice
            WHEN ISNULL(b.ismobile,0)=0 THEN b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)- b.inprice
            WHEN z.mkc_id IS NOT NULL AND (b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)) &lt; k.staticPrice THEN 0.0 ELSE b.price-ISNULL(b.youhuiPrice,0)-isnull(jifenPrice,0)-k.staticPrice END checkProfit_,
        </if>
        22 orderDetailType,
        p.pLabel salaryProductLabel,
        s.returnDate refundDate,
        s.tradeDate1 tradeDate,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        3 userType,
        0 refunded
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
    </sql>
    <sql id="sale_order_columns_3">
        SELECT 'marketing' orderKind,
        '1-2' orderType,
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        p.cid,
        p.productid productId,
        <choose>
            <when test="isJiuJi">
                case when isnull(b.isOnShop, 0)=1 and isnull(s.subtype, 0) in (2,3,4,5,6,7,11,16,17,18,19,22)  then 1 else s.subtype end orderVType,
            </when>
            <otherwise>
                s.subtype orderVType,
            </otherwise>
        </choose>
        s.delivery orderDelivery,
        b.basket_id basketId,
        p.brandID brandId,
        b.ppriceid ppId,
        b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        ISNULL(b.price2,b.price) - k.inprice - ISNULL(k.addprice,0) profit,
        null profit1,
        null profit2,
        null profit3,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            NULL checkProfit_,
        </if>
        -1 orderDetailType,
        p.pLabel salaryProductLabel,
        s.returnDate refundDate,
        s.tradeDate1 tradeDate,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
    </sql>
    <sql id="sale_order_columns_3_from">
        FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = b.basket_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
    </sql>

    <select id="listReturnOrder" resultType="com.jiuji.oa.oacore.salary.entity.vo.RetrunOrderEx">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{startDate} AND #{endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        b.price saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        s.pay_time tradeDate,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        1 userType,
        CASE
        WHEN ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1
        ELSE NULL
        END refundDate ,
        0 refunded
        <include refid="return_order_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{startDate} AND #{endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        b.price saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        s.pay_time tradeDate ,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit ,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        1 userType,
        CASE
        WHEN ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1
        ELSE NULL
        END refundDate ,
        1 refunded
        <include refid="return_order_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{startDate} AND #{endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        b.price saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        s.pay_time tradeDate ,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit ,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        2 userType,
        CASE
        WHEN ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1
        ELSE NULL
        END refundDate ,
        0 refunded
        <include refid="return_order_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{startDate} AND #{endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        b.price saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        s.pay_time tradeDate ,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit ,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        2 userType,
        CASE
        WHEN ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1
        ELSE NULL
        END refundDate ,
        1 refunded
        <include refid="return_order_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{startDate} AND #{endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        b.price saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        s.pay_time tradeDate ,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit ,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        5 userType,
        null refundDate ,
        0 refunded
        <include refid="return_order_from"></include>
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkpriceUser
        WHERE s.sub_check = 3
        and k.issalf=0
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}

        union all

        <include refid="recover_marketInfo_user_type_2-3"></include>
    </select>


    <sql id="recover_marketInfo_user_type_2-3">
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        1 userType,
        NULL refundDate,
        0 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        UNION ALL
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        1 userType,
        rs.returnDate refundDate,
        1 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        UNION ALL
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        2 userType,
        NULL refundDate,
        0 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        UNION ALL
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        2 userType,
        rs.returnDate refundDate,
        1 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        UNION ALL
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        5 userType,
        NULL refundDate,
        0 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkpriceUser
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        UNION ALL
        SELECT
            b.id basketId,
            p.brandID brandId,
            p.cid,
            1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        rs.tradeDate1 tradeDate ,
        ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) trueProfit ,
        CASE WHEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        5 userType,
        rs.returnDate refundDate,
        1 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkpriceUser
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
    </sql>

    <sql id="return_order_from">
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
    </sql>
    <select id="listAfterOrder" resultType="com.jiuji.oa.salary.AfterOrder">
        SELECT
        '3-3' orderType ,
        1 AS userType ,
        <include refid="after_order_columns_3_3"></include>
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.inuser=cc.ch999_name
        <include refid="after_order_where"></include>
        AND s.issoft=0

        UNION ALL

        SELECT
        '3-3' orderType ,
        2 AS userType ,
        <include refid="after_order_columns_3_3"></include>
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.weixiuren=cc.ch999_name
        <include refid="after_order_where"></include>
        AND s.issoft=0

        UNION ALL

        SELECT
        '3-3' orderType ,
        1 AS userType ,
        'afterSales' orderKind ,
        s.id subId ,
        s.areaid areaId,
        s.basket_id basketId ,
        CASE
        WHEN ISNULL(s.stats,0)=1 THEN 1
        WHEN ISNULL(s.stats,0)=3 THEN 3
        ELSE 0 END orderRepairStatus ,
        CASE
        WHEN ISNULL(s.baoxiu,0)=1 THEN 1
        WHEN ISNULL(s.baoxiu,0)=2 THEN 2
        WHEN ISNULL(s.baoxiu,0)=3 THEN 3
        ELSE 0 END orderWarrantyStatus ,
        s.ServiceType orderServiceType ,
        CASE
        WHEN ISNULL(s.RepairLevel,0)=1 THEN 1
        WHEN ISNULL(s.RepairLevel,0)=2 THEN 2
        WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank ,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE
        WHEN ISNULL(s.ishuishou,0)=1 THEN 1
        WHEN ISNULL(s.isXcMkc,0)=1 THEN 1
        ELSE 0 END orderRepairProductType ,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource ,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit ,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit ,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit ,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit ,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit ,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        NULL trueProfit,
        NULL trueSalaryLossRelation,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        w.tuidtime refundDate ,
        1 refunded,
        s.offtime tradeDate,
        s.inuser inUser,s.weixiuren weiXiuRen,s.webtype2 orderServiceMode
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=w.ppriceid
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.inuser=cc.ch999_name
        <include refid="after_tui_order_where"></include>
        AND s.issoft=0

        UNION ALL

        SELECT
        '3-3' orderType ,
        2 AS userType ,
        'afterSales' orderKind ,
        s.id subId ,
        s.areaid areaId,
        s.basket_id basketId ,
        CASE
        WHEN ISNULL(s.stats,0)=1 THEN 1
        WHEN ISNULL(s.stats,0)=3 THEN 3
        ELSE 0 END orderRepairStatus ,
        CASE
        WHEN ISNULL(s.baoxiu,0)=1 THEN 1
        WHEN ISNULL(s.baoxiu,0)=2 THEN 2
        WHEN ISNULL(s.baoxiu,0)=3 THEN 3
        ELSE 0 END orderWarrantyStatus ,
        s.ServiceType orderServiceType ,
        CASE
        WHEN ISNULL(s.RepairLevel,0)=1 THEN 1
        WHEN ISNULL(s.RepairLevel,0)=2 THEN 2
        WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank ,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE
        WHEN ISNULL(s.ishuishou,0)=1 THEN 1
        WHEN ISNULL(s.isXcMkc,0)=1 THEN 1
        ELSE 0 END orderRepairProductType ,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource ,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit ,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit ,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit ,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit ,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit ,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        NULL trueProfit,
        NULL trueSalaryLossRelation,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        w.tuidtime refundDate,
        1 refunded,
        s.offtime tradeDate,
        s.inuser inUser,s.weixiuren weiXiuRen,s.webtype2 orderServiceMode
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=w.ppriceid
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.weixiuren=cc.ch999_name
        <include refid="after_tui_order_where"></include>
        AND s.issoft=0

        UNION ALL

        SELECT
        '3-2' orderType ,
        1 AS userType ,
        <include refid="after_order_columns"></include>
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.inuser=cc.ch999_name
        <include refid="after_order_where"></include>
        AND s.issoft=1

        UNION ALL

        SELECT
        '3-2' orderType ,
        2 AS userType ,
        <include refid="after_order_columns"></include>
        LEFT JOIN dbo.ch999_user cc WITH(NOLOCK) ON s.weixiuren=cc.ch999_name
        <include refid="after_order_where"></include>
        AND s.issoft=1

        UNION ALL

        SELECT
        '3-1' orderType,
        1 userType,
        'afterSales' orderKind,
        ms.id subId,
        ms.areaid areaId,
        ms.id basketId,
        NULL orderRepairStatus,
        NULL orderWarrantyStatus,
        NULL orderServiceType,
        NULL orderRepairRank,
        NULL orderRepairType,
        NULL orderRepairProductType,
        NULL orderSource,
        NULL orderReturnLimit,
        NULL orderManualFeeLimit,
        NULL orderMaintenanceFeeLimit,
        NULL orderOldReplaceLimit,
        NULL orderBackRepairLimit,
        NULL orderFeeLimit,
        NULL orderCostLimit,
        1 [count],
        NULL trueProfit,
        0 trueSalaryLossRelation,
        NULL saleAmount,
        NULL oldPartGrossProfit,
        p.cid cid,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        ms.ppriceid ppid,
        NULL wxkcoutputId,
        NULL productId,
        NULL refundDate,
        NULL refunded,
        ms.modidate tradeDate,
        NULL inUser,NULL weiXiuRen,NULL orderServiceMode
        FROM dbo.msoft ms with(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = ms.ppriceid
        LEFT JOIN dbo.ch999_user cc with(nolock) ON cc.ch999_name=ms.inuser
        WHERE isnull(isticheng,0)=1
        AND ms.modidate BETWEEN #{startDate} AND #{endDate}
        AND cc.ch999_id=#{ch999Id}
    </select>

    <sql id="after_order_columns_3_3">
        'afterSales' orderKind ,
        s.id subId ,
        s.areaid areaId,
        s.basket_id basketId ,
        CASE
            WHEN ISNULL(s.stats,0)=1 THEN 1
            WHEN ISNULL(s.stats,0)=3 THEN 3
            ELSE 0 END orderRepairStatus ,
        CASE
            WHEN ISNULL(s.baoxiu,0)=1 THEN 1
            WHEN ISNULL(s.baoxiu,0)=2 THEN 2
            WHEN ISNULL(s.baoxiu,0)=3 THEN 3
            ELSE 0 END orderWarrantyStatus ,
        s.ServiceType orderServiceType ,
        CASE
            WHEN ISNULL(s.RepairLevel,0)=1 THEN 1
            WHEN ISNULL(s.RepairLevel,0)=2 THEN 2
            WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank ,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE
            WHEN ISNULL(s.ishuishou,0)=1 THEN 1
            WHEN ISNULL(s.isXcMkc,0)=1 THEN 1
            ELSE 0 END orderRepairProductType ,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource ,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit ,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit ,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit ,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit ,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit ,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        NULL trueProfit,
        NULL trueSalaryLossRelation,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        th.dtime refundDate ,
        CASE WHEN ISNULL(th.tuihuan_kind,0)=3 THEN 1 ELSE 0 END refunded,
        s.offtime tradeDate,
        s.inuser inUser,s.weixiuren weiXiuRen,s.webtype2 orderServiceMode
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=w.ppriceid
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
    </sql>


    <sql id="after_order_columns">
        'afterSales' orderKind ,
        s.id subId ,
        s.areaid areaId,
        s.basket_id basketId ,
        CASE
            WHEN ISNULL(s.stats,0)=1 THEN 1
            WHEN ISNULL(s.stats,0)=3 THEN 3
            ELSE 0 END orderRepairStatus ,
        CASE
            WHEN ISNULL(s.baoxiu,0)=1 THEN 1
            WHEN ISNULL(s.baoxiu,0)=2 THEN 2
            WHEN ISNULL(s.baoxiu,0)=3 THEN 3
            ELSE 0 END orderWarrantyStatus ,
        s.ServiceType orderServiceType ,
        CASE
            WHEN ISNULL(s.RepairLevel,0)=1 THEN 1
            WHEN ISNULL(s.RepairLevel,0)=2 THEN 2
            WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank ,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE
            WHEN ISNULL(s.ishuishou,0)=1 THEN 1
            WHEN ISNULL(s.isXcMkc,0)=1 THEN 1
            ELSE 0 END orderRepairProductType ,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource ,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit ,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit ,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit ,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit ,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit ,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        NULL trueProfit,
        NULL trueSalaryLossRelation,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        th.dtime refundDate ,
        CASE WHEN ISNULL(th.tuihuan_kind,0)=3 THEN 1 ELSE 0 END refunded,
        s.offtime tradeDate,
        s.inuser inUser,s.weixiuren weiXiuRen,s.webtype2 orderServiceMode
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=s.ppriceid
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
    </sql>

    <sql id="after_order_where">
        OUTER APPLY (
            SELECT TOP 1 wk.ppriceid
            FROM dbo.wxkcoutput wk WITH(NOLOCK)
            WHERE wk.wxid=s.id
            AND wk.ppriceid=0
            AND ISNULL(wk.stats,0) &lt;&gt;3
        ) rg
        OUTER APPLY (
            SELECT TOP 1 sh.id
            FROM shouhou_huishou sh WITH(NOLOCK)
            WHERE ISNULL(ishuanhuo,0)=1
            AND sh.shouhou_id=s.id
        ) hh
        OUTER APPLY (
            SELECT TOP 1 t.id,t.dtime,t.tuihuan_kind
            FROM dbo.shouhou_tuihuan t WITH(NOLOCK)
            WHERE t.shouhou_id=s.id
            AND t.tuihuan_kind IN(1,2,3,4)
            AND ISNULL(t.isdel,0)=0
        ) th
        OUTER APPLY(
            SELECT SUM(ISNULL(CASE WHEN sh.toareaid=360 THEN sh.inprice ELSE sh.saleprice END,0) - ISNULL(sh.price,0)) AS jjmaoli
            FROM shouhou_huishou sh with(nolock)
            WHERE sh.shouhou_id=s.id
            AND ISNULL(s.wxkind,0) &lt;&gt;5
            AND ISNULL(s.issoft,0) = 0
            AND ((sh.sdtime IS NOT NULL
            AND ISNULL(sh.toareaid,0)=360)
            OR (ISNULL(sh.toareaid,0)!=360
            AND sh.saledate IS NOT NULL
            AND sh.issale=1
            AND sh.isfan=0))
        ) jjml
        WHERE ISNULL(s.isquji,0)=1
        AND ISNULL(s.stats,0)=1
        AND ISNULL(s.isquji,0)=1
        AND s.xianshi=1
        AND s.offtime BETWEEN #{startDate} AND #{endDate}
        AND cc.ch999_id=#{ch999Id}
        AND isnull(w.part_type, 0)!=1
    </sql>


    <sql id="after_tui_order_where">
        OUTER APPLY (
            SELECT TOP 1 wk.ppriceid
            FROM dbo.wxkcoutput wk WITH(NOLOCK)
            WHERE wk.wxid=s.id
            AND wk.ppriceid=0
            AND ISNULL(wk.stats,0) &lt;&gt;3
        ) rg
        OUTER APPLY (
            SELECT TOP 1 sh.id
            FROM shouhou_huishou sh WITH(NOLOCK)
            WHERE ISNULL(ishuanhuo,0)=1
            AND sh.shouhou_id=s.id
        ) hh
        OUTER APPLY (
            SELECT TOP 1 t.id,t.dtime,t.tuihuan_kind
            FROM dbo.shouhou_tuihuan t WITH(NOLOCK)
            WHERE t.shouhou_id=s.id
            AND t.tuihuan_kind IN(1,2,3,4)
            AND ISNULL(t.isdel,0)=0
        ) th
        OUTER APPLY(
            SELECT SUM(ISNULL(CASE WHEN sh.toareaid=360 THEN sh.inprice ELSE sh.saleprice END,0) - ISNULL(sh.price,0)) AS jjmaoli
            FROM shouhou_huishou sh with(nolock)
            WHERE sh.shouhou_id=s.id
            AND ISNULL(s.wxkind,0) &lt;&gt;5
            AND ISNULL(s.issoft,0) = 0
            AND ((sh.sdtime IS NOT NULL
            AND ISNULL(sh.toareaid,0)=360)
            OR (ISNULL(sh.toareaid,0)!=360
            AND sh.saledate IS NOT NULL
            AND sh.issale=1
            AND sh.isfan=0))
        ) jjml
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
        WHERE ISNULL(s.isquji,0)=1
        AND ISNULL(s.stats,0)=1
        AND ISNULL(s.isquji,0)=1
        AND s.xianshi=1
        AND w.tuidtime BETWEEN #{startDate} AND #{endDate}
        and (CASE WHEN ISNULL(w.stats,0)=3 THEN 1 ELSE 0 END)=1
        AND cc.ch999_id=#{ch999Id}
        AND isnull(w.part_type, 0)!=1
    </sql>

    <select id="listOperatorOrder" resultType="com.jiuji.oa.salary.OperatorOrder">
        SELECT
            0 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            1 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock) ON ob.basketId = b.basket_id
            LEFT JOIN ch999_user cc with (nolock)  ON b.seller = cc.ch999_name
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN productinfo p with (nolock)  ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.isdel=0
          AND isnull(ob.isdel, 0) = 0 and ob.status = 1
        union all

        SELECT
            0 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            1 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock) ON ob.basketId = b.basket_id
            LEFT JOIN ch999_user cc with (nolock)  ON b.seller = cc.ch999_name
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN productinfo p with (nolock)  ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.status in(1,2)
          AND ob.isdel=0
          AND isnull(ob.isdel, 0) = 0 and ob.returnTime > s.tradeDate1
        union all

        SELECT
            0 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            2 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock) ON ob.basketId = b.basket_id
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN ch999_user cc with (nolock)  ON s.trader = cc.ch999_name
            LEFT JOIN productinfo p with (nolock)  ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.isdel=0
          AND isnull(ob.isdel, 0) = 0 and ob.status = 1
        union all

        SELECT
            0 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            2 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock) ON ob.basketId = b.basket_id
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN ch999_user cc with (nolock)  ON s.trader = cc.ch999_name
            LEFT JOIN productinfo p with (nolock)  ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            s.tradeDate1 BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.status in(1,2)
          AND ob.isdel=0
          AND isnull(ob.isdel, 0) = 0 and ob.returnTime > s.tradeDate1
        union all

        SELECT
            1 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            1 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock)  ON ob.basketId = b.basket_id
            LEFT JOIN ch999_user cc with (nolock)  ON b.seller = cc.ch999_name
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN productinfo p  with (nolock) ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            ob.returnTime BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.isdel=0
          and s.tradeDate1 is not NULL and ob.returnTime > s.tradeDate1

        union all

        SELECT
            1 isReturnOrder,
            'operator' orderKind,
            '4-1' orderType,
            s.subtype orderVType,
            s.sub_id subId,
            s.areaid areaId,
            2 userType,
            b.basket_count count,
            b.price2 saleAmount,
            ob.precommission estimatedCommission,
            ob.actCommission actualCommission,
            ob.agentPoints agencyPoints,
            ob.settlementMoney settlementAmount,
            ob.bounty,
            ob.preAbility,
            ob.deductionMoney,
            ob.status,
            ob.returnTime,
            p.product_id productId,
            p.brandID brandId,
            b.ppriceid ppId,
            p.cid cid,
            p.ismobile1 salaryProductType,
            s.tradeDate1 AS tradeDate,
            0 trueSalaryLossRelation
        FROM
            OperatorBasket ob with (nolock)
            LEFT JOIN basket b with (nolock)  ON ob.basketId = b.basket_id
            LEFT JOIN sub s with (nolock)  ON s.sub_id = b.sub_id
            LEFT JOIN ch999_user cc with (nolock)  ON s.trader = cc.ch999_name
            LEFT JOIN productinfo p  with (nolock) ON b.ppriceid = p.ppriceid
            LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
            LEFT JOIN category c with (nolock)  ON p.cid = c.id
        WHERE
            ob.returnTime BETWEEN #{startDate} AND #{endDate}
          AND cc.ch999_id = #{ch999Id}
          AND ob.isdel=0
          and s.tradeDate1 is not NULL and ob.returnTime > s.tradeDate1
    </select>

    <select id="listSaleOrderNew" parameterType="com.jiuji.oa.salary.CalculateSaleReq" resultType="com.jiuji.oa.salary.SaleOrder">
        SELECT t.*,
        t.count * t.price saleAmount,
        t.count * t.profit trueProfit,
        t.count * t.profit1 trueProfit1,
        t.count * t.profit2 trueProfit2,
        t.count * t.profit3 trueProfit3,
        CASE WHEN t.profit &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation
        <if test="isOpenCheck!=null and isOpenCheck>0">
            ,t.count * t.checkProfit_ checkProfit
            ,CASE WHEN  t.checkProfit_ &lt; 0 THEN 1 ELSE 0 END checkSalaryLossRelation
        </if>
        FROM
        (
        SELECT 'marketing' orderKind, '1-1'  orderType, s.sub_id subId,k.id mkcId,p.cid,p.productid productId,s.subtype orderVType,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
        when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
        when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
        else b.price2 - isnull(b.inprice,b.price2) end profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            b.price2 - (CASE WHEN b.ismobile = 1 AND isnull(b.examPrice,k.examPrice) > 0 then isnull(b.examPrice,k.examPrice)
            WHEN b.ismobile = 1 then isnull(k.staticPrice,price2)
            ELSE isnull(b.examPrice,isnull(b.inprice,price2)) END) checkProfit_,
        </if>
        b.type orderDetailType
        ,p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,0 refunded,s.areaid AS areaId
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check in (3,9) and isnull(b.type,0) &lt; &gt; 22 AND s.tradeDate1 BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        UNION ALL

        SELECT 'marketing' orderKind,'1-1'  orderType, s.sub_id subId,k.id mkcId,p.cid,p.productid productId,
        s.subtype orderVType,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
        when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
        when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
        else b.price2 - isnull(b.inprice,b.price2) end profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            b.price2 - (CASE WHEN b.ismobile = 1 AND isnull(b.examPrice,k.examPrice) > 0 then isnull(b.examPrice,k.examPrice)
            WHEN b.ismobile = 1 then isnull(k.staticPrice,price2)
            ELSE isnull(b.examPrice,isnull(b.inprice,price2)) END) checkProfit_,
        </if>
        b.type  orderDetailType
        ,p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,1 refunded,s.areaid AS areaId
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 and isnull(b.type,0) &lt; &gt; 22 AND s.returnDate BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        )t

        WHERE 1=1
        <if test="calculateSaleReq.cid!=null and calculateSaleReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateSaleReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateSaleReq.productId!=null and calculateSaleReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateSaleReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateSaleReq.ppId!=null and calculateSaleReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateSaleReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>

        <if test="calculateSaleReq.orderVType!=null and calculateSaleReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateSaleReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDelivery!=null and calculateSaleReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateSaleReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDetailType!=null and calculateSaleReq.orderDetailType.size>0">
            AND (t.orderDetailType IN
            <foreach collection="calculateSaleReq.orderDetailType" item="orderDetailType" separator="," open="(" close=")">
                #{orderDetailType}
            </foreach>
                or t.orderDetailType is null or t.orderDetailType=0)
        </if>
    </select>
    <select id="listSaleOrderGoodProduct" parameterType="com.jiuji.oa.salary.CalculateSaleReq" resultType="com.jiuji.oa.salary.SaleOrder">
        SELECT t.*,
        t.count * t.price saleAmount,
        t.count * t.profit trueProfit
        <if test="isOpenCheck!=null and isOpenCheck>0">
            ,t.count * t.checkProfit_ checkProfit
        </if>
        FROM
        (
        SELECT 'marketing' orderKind,'1-2' orderType,s.sub_id,k.id mkcId,p.cid,p.productid,
        <choose>
            <when test="isJiuJi">
                case when isnull(b.isOnShop, 0)=1 and isnull(s.subtype, 0) in (2,3,4,5,6,7,11,16,17,18,19,22)  then 1 else s.subtype end orderVType,
            </when>
            <otherwise>
                s.subtype orderVType,
            </otherwise>
        </choose>
               s.sub_pay,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        ISNULL(b.price2,b.price) - k.inprice - ISNULL(k.addprice,0) profit,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            NULL checkProfit_,
        </if>
        -1 orderDetailType,
        p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,1 refunded,s.areaid AS areaId
        FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = b.basket_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (3,9) AND s.tradeDate1 BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        UNION ALL

        SELECT 'marketing' orderKind,'1-2' orderType,s.sub_id,k.id mkcId,p.cid,p.productid,
        <choose>
            <when test="isJiuJi">
                case when isnull(b.isOnShop, 0)=1 and isnull(s.subtype, 0) in (2,3,4,5,6,7,11,16,17,18,19,22)  then 1 else s.subtype end orderVType,
            </when>
            <otherwise>
                s.subtype orderVType,
            </otherwise>
        </choose>
          s.sub_pay,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        ISNULL(b.price2,b.price) - k.inprice - ISNULL(k.addprice,0) profit,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            NULL checkProfit_,
        </if>
        -1 orderDetailType,
        p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,0 refunded,s.areaid AS areaId
        FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = b.basket_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(s.saleType,0) = 0 AND ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 AND s.returnDate BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t

        WHERE 1=1
        <if test="calculateSaleReq.cid!=null and calculateSaleReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateSaleReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateSaleReq.productId!=null and calculateSaleReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateSaleReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateSaleReq.ppId!=null and calculateSaleReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateSaleReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>

        <if test="calculateSaleReq.orderVType!=null and calculateSaleReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateSaleReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDelivery!=null and calculateSaleReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateSaleReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDetailType!=null and calculateSaleReq.orderDetailType.size>0">
            AND (t.orderDetailType IN
            <foreach collection="calculateSaleReq.orderDetailType" item="orderDetailType" separator="," open="(" close=")">
                #{orderDetailType}
            </foreach>
            or t.orderDetailType is null or t.orderDetailType=0)
        </if>
    </select>
    <select id="listSaleOrderBestProduct" parameterType="com.jiuji.oa.salary.CalculateSaleReq" resultType="com.jiuji.oa.salary.SaleOrder">
        SELECT t.*,
        t.count * t.price saleAmount,
        t.count * t.profit trueProfit,
        t.count * t.profit1 trueProfit1,
        t.count * t.profit2 trueProfit2,
        t.count * t.profit3 trueProfit3,
        CASE WHEN t.profit &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation
        <if test="isOpenCheck!=null and isOpenCheck>0">
            ,t.count * t.checkProfit_ checkProfit
            ,CASE WHEN  t.checkProfit_ &lt; 0 THEN 1 ELSE 0 END checkSalaryLossRelation
        </if>
        FROM
        (
        SELECT 'marketing' orderKind, '1-3'  orderType, s.sub_id subId,k.id mkcId,p.cid,p.productid productId,s.subtype orderVType,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
        when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
        when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
        else b.price2 - isnull(b.inprice,b.price2) end profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            b.price2 - (CASE WHEN b.ismobile = 1 AND isnull(b.examPrice,k.examPrice) > 0 then isnull(b.examPrice,k.examPrice)
            WHEN b.ismobile = 1 then isnull(k.staticPrice,price2)
            ELSE isnull(b.examPrice,isnull(b.inprice,price2)) END) checkProfit_,
        </if>
        b.type  orderDetailType
        ,p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,1 refunded,s.areaid AS areaId
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check in (3,9) and b.type = 22 AND s.tradeDate1 BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        UNION ALL

        SELECT 'marketing' orderKind,'1-3'  orderType, s.sub_id subId,k.id mkcId,p.cid,p.productid productId,
        s.subtype orderVType,s.delivery orderDelivery
        ,b.basket_id basketId,p.brandID brandId,b.ppriceid ppId,b.ismobile salaryProductType,
        (b.price-isnull(b.youhuiPrice,0)-isnull(b.jifenPrice,0)) price,
        CASE WHEN ISNULL(b.ismobile,0)=1 AND ISNULL(k.id,0)>0 THEN 1 else b.basket_count end count,
        case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
        when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
        when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
        else b.price2 - isnull(b.inprice,b.price2) end profit,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit1,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice)
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit2,
        CASE
        WHEN ISNULL(b.ismobile, 0) = 1 THEN (b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0)) - isnull(k.transferPrice,k.inbeihuoprice) + (isnull(k.modifyPrice,0)+isnull(k.inner_price,0)) + (ISNULL(k.fanli,0)+ISNULL(k.protectPrice,0))
        ELSE b.price - ISNULL(b.youhuiPrice, 0) - isnull(jifenPrice, 0) - b.inprice END profit3,
        CASE WHEN isnull(b.youhuiPrice,0)=0 then 0 else 1 end isNoCoupon,
        CASE WHEN sa.sub_provide is not null and sa.sub_provide!='' then 1 else 0 end hasProvide,
        <if test="isOpenCheck!=null and isOpenCheck>0">
            b.price2 - (CASE WHEN b.ismobile = 1 AND isnull(b.examPrice,k.examPrice) > 0 then isnull(b.examPrice,k.examPrice)
            WHEN b.ismobile = 1 then isnull(k.staticPrice,price2)
            ELSE isnull(b.examPrice,isnull(b.inprice,price2)) END) checkProfit_,
        </if>
        b.type  orderDetailType
        ,p.pLabel salaryProductLabel,s.returnDate refundDate,s.tradeDate1 tradeDate,NULL userType,0 refunded,s.areaid AS areaId
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        left join dbo.product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        left join dbo.SubAddress sa with(nolock) on s.sub_id = sa.sub_id
        WHERE ISNULL(b.isdel,0) = 0 AND s.sub_check = 9 and b.type = 22 AND s.returnDate BETWEEN #{calculateSaleReq.startDate} AND #{calculateSaleReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateSaleReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        )t

        WHERE 1=1
        <if test="calculateSaleReq.cid!=null and calculateSaleReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateSaleReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateSaleReq.productId!=null and calculateSaleReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateSaleReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateSaleReq.ppId!=null and calculateSaleReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateSaleReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>

        <if test="calculateSaleReq.orderVType!=null and calculateSaleReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateSaleReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDelivery!=null and calculateSaleReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateSaleReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
        <if test="calculateSaleReq.orderDetailType!=null and calculateSaleReq.orderDetailType.size>0">
            AND (t.orderDetailType IN
            <foreach collection="calculateSaleReq.orderDetailType" item="orderDetailType" separator="," open="(" close=")">
                #{orderDetailType}
            </foreach>
            or t.orderDetailType is null or t.orderDetailType=0)
        </if>
    </select>

    <select id="listReturnOrderReturn" parameterType="com.jiuji.oa.salary.CalculateReturnReq" resultType="com.jiuji.oa.salary.ReturnOrder">
        SELECT
        t.basketId,
        t.brandID brandId,
        t.cid,
        t.COUNT,
        t.orderDelivery,
        t.orderKind,
        t.orderPayType ,
        t.orderType,
        t.ppId,
        t.orderVType,
        t.productId,
        t.salaryProductType,
        t.saleAmount,
        t.returnAmount,
        t.subId,
        t.mkcId,
        t.tradeDate,
        t.trueProfit,
        t.salaryLossRelation AS trueSalaryLossRelation,
        0 checkSalaryLossRelation,
        t.difference,
        t.userType,
        t.refundDate,
        t.refunded,
        t.areaid AS areaId
        FROM (
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-1' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        k.id mkcId,
        s.pay_time tradeDate,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0)&lt;0 THEN 1 ELSE 0 END salaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        s.areaid,
        CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
        0 refunded
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel, 0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.kinds&lt;&gt;1
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        '2-1' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        p.productid productId,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        k.id mkcId,
        s.pay_time tradeDate,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2, rb.price) - k.inprice - ISNULL(k.addprice, 0) ELSE NULL END trueProfit,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2, rb.price) - k.inprice - ISNULL(k.addprice, 0) &lt;0 THEN 1 ELSE 0 END salaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        s.areaid,
        CASE WHEN ISNULL(rs.saleType, 0) = 1 AND ISNULL(rs.sub_to, '') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
        1 refunded
        FROM dbo.recover_sub s WITH (NOLOCK)
        INNER JOIN dbo.recover_basket b WITH (NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.recover_mkc k WITH (NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_marketSubInfo rb with (nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with (nolock) ON rb.sub_id = rs.sub_id
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel, 0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType, 0) = 1
        AND ISNULL(rs.sub_to, '') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.kinds&lt;&gt;1
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        <!--
       <if test="isJiuJi">

           UNION ALL

           SELECT
           b.id basketId,
           p.brandID brandId,
           p.cid,
           -1 COUNT,
           s.sub_delivery orderDelivery,
           'recycle' orderKind,
           s.sub_pay orderPayType,
           '2-1' orderType,
           <choose>
               <when test="isJiuJi">
                   CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
               </when>
               <otherwise>
                   CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
               </otherwise>
           </choose>
           b.replacement_insurance_price replacementInsurancePrice,
           p.ppriceid ppId,
           CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
           WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
           WHEN  isnull(s.recover_subType,0)=1 THEN 3
           WHEN  isnull(s.recover_subType,0)=2 THEN 4
           END orderVType,
           p.productid productId,
           b.ismobile salaryProductType,
           -b.price saleAmount,
           s.sub_id subId,
           k.id mkcId,
           rs.tradeDate1 tradeDate,
           null trueProfit,
           null salaryLossRelation,
           null difference,
           NULL userType,
           s.areaid,
           NULL refundDate,
           NULL refunded
           FROM dbo.recover_marketInfo rs with(nolock)
           INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
           INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
           INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
           INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
           INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
           WHERE rs.sub_check in (3,9)
           AND ISNULL(k.ishouhou,0) = 0
           AND ISNULL(rb.isdel,0) = 0
           AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
           AND isnull(s.pay_time, s.ruku_time)>'2023-08-15 00:00:00'
           AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
           and
           <choose>
               <when test="isJiuJi">
                   (isnull(rb.price2, rb.price) - k.inprice)
               </when>
               <otherwise>
                   (ISNULL( rb.price2, rb.price ) - ISNULL( k.addprice, 0 ) - ISNULL(k.inprice,0) - ISNULL(rb.auction_fee,0))
               </otherwise>
           </choose>
           &lt;-100
            AND s.areaid IN
            <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>-->

        ) t
        WHERE 1=1
        <if test="calculateReturnReq.orderType!=null">
            AND t.orderType = #{calculateReturnReq.orderType}
        </if>
        <if test="calculateReturnReq.cid!=null and calculateReturnReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateReturnReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateReturnReq.productId!=null and calculateReturnReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateReturnReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateReturnReq.ppId!=null and calculateReturnReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateReturnReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderVType!=null and calculateReturnReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateReturnReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderPayType!=null and calculateReturnReq.orderPayType.size>0">
            AND t.orderPayType IN
            <foreach collection="calculateReturnReq.orderPayType" item="orderPayTypeId" separator="," open="(" close=")">
                #{orderPayTypeId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderDelivery!=null and calculateReturnReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateReturnReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
        <if test="calculateReturnReq.difference!=null">
            AND t.difference = #{calculateReturnReq.difference}
        </if>
    </select>
    <select id="listReturnOrderHelpSell" parameterType="com.jiuji.oa.salary.CalculateReturnReq" resultType="com.jiuji.oa.salary.ReturnOrder">
        SELECT
        t.basketId,
        t.brandID brandId,
        t.cid,
        t.COUNT,
        t.orderDelivery,
        t.orderKind,
        t.orderPayType ,
        t.orderType,
        t.ppId,
        t.orderVType,
        t.productId,
        t.salaryProductType,
        t.saleAmount,
        t.returnAmount,
        t.subId,
        t.mkcId,
        t.tradeDate,
        t.trueProfit,
        t.salaryLossRelation AS trueSalaryLossRelation,
        0 checkSalaryLossRelation,
        t.difference,
        t.userType,
        t.refundDate,
        t.refunded,
        t.areaid AS areaId
        FROM (
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        k.id mkcId,
        s.pay_time tradeDate,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0)&lt;0 THEN 1 ELSE 0 END salaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        s.areaid,
        CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
        0 refunded
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel, 0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.kinds=1
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null and k.first_on_salf_time BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate} THEN 1 ELSE 0 END) END goodProductDisplay,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        k.id mkcId,
        s.pay_time tradeDate ,
        s.areaid,
        CASE WHEN rs.sub_check = 3 THEN ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0) ELSE NULL END trueProfit ,
        CASE WHEN rs.sub_check = 3 AND ISNULL(rb.price2,rb.price) - k.inprice - ISNULL(k.addprice,0)&lt;0 THEN 1 ELSE 0 END salaryLossRelation ,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate ,
        1 refunded
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.kinds=1
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t
        WHERE 1 = 1
        <if test="calculateReturnReq.orderType!=null">
            AND t.orderType = #{calculateReturnReq.orderType}
        </if>
        <if test="calculateReturnReq.cid!=null and calculateReturnReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateReturnReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateReturnReq.productId!=null and calculateReturnReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateReturnReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateReturnReq.ppId!=null and calculateReturnReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateReturnReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderVType!=null and calculateReturnReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateReturnReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderPayType!=null and calculateReturnReq.orderPayType.size>0">
            AND t.orderPayType IN
            <foreach collection="calculateReturnReq.orderPayType" item="orderPayTypeId" separator="," open="(" close=")">
                #{orderPayTypeId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderDelivery!=null and calculateReturnReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateReturnReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
    </select>

    <select id="listAfterOrderSoftUnderWarranty" parameterType="com.jiuji.oa.salary.CalculateAfterReq" resultType="com.jiuji.oa.salary.AfterOrder">
        SELECT
        t.orderKind,
        t.orderType,
        t.subId,
        t.basketId,
        t.userType,
        t.ppid,
        t.tradedate,
        t.orderRepairStatus,
        t.orderServiceType,
        t.orderWarrantyStatus,
        t.orderRepairRank,
        t.orderRepairType,
        t.orderRepairProductType,
        t.orderSource,
        t.orderReturnLimit,
        t.orderManualFeeLimit,
        t.orderMaintenanceFeeLimit,
        t.orderOldReplaceLimit,
        t.orderBackRepairLimit,
        t.orderFeeLimit,
        t.orderCostLimit,
        t.[count],
        t.checkProfit,
        t.checkSalaryLossRelation,
        t.trueProfit,
        t.trueSalaryLossRelation,
        t.saleAmount,
        t.oldPartGrossProfit,
        t.salaryProductType,
        t.brandId brandId,
        t.cid,
        t.productId,
        t.refundDate,
        t.areaid AS areaId
        FROM (
        SELECT
        'afterSales' orderKind,
        '3-1' orderType,
        ms.id subId,
        ms.id basketId,
        NULL userType,
        ms.ppriceid ppid,
        ms.modidate tradedate,
        NULL orderRepairStatus,
        NULL orderServiceType,
        NULL orderWarrantyStatus,
        NULL orderRepairRank,
        NULL orderRepairType,
        NULL orderRepairProductType,
        NULL orderSource,
        NULL orderReturnLimit,
        NULL orderManualFeeLimit,
        NULL orderMaintenanceFeeLimit,
        NULL orderOldReplaceLimit,
        NULL orderBackRepairLimit,
        NULL orderFeeLimit,
        NULL orderCostLimit,
        1 [count],
        NULL checkProfit,
        0 checkSalaryLossRelation,
        NULL trueProfit,
        0 trueSalaryLossRelation,
        NULL saleAmount,
        NULL oldPartGrossProfit,
        p.ismobile1 salaryProductType,
        p.brandID brandId,
        p.cid,
        NULL productId,
        NULL refundDate,
        ms.areaid
        FROM dbo.msoft ms with(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = ms.ppriceid
        WHERE isnull(isticheng,0)=1
        AND ms.modidate BETWEEN #{calculateAfterReq.startDate} AND #{calculateAfterReq.endDate}
        AND ms.areaid IN
        <foreach collection="calculateAfterReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t
        WHERE 1=1
        <if test="calculateAfterReq.ppId!=null and calculateAfterReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateAfterReq
            .ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
    </select>
    <select id="listAfterOrderNotSoftUnderWarranty" parameterType="com.jiuji.oa.salary.CalculateAfterReq" resultType="com.jiuji.oa.salary.AfterOrder">
        SELECT
        t.orderKind ,
        t.orderType ,
        t.subId ,
        t.brandId ,
        t.salaryProductType,
        t.basketId ,
        t.userType ,
        t.orderRepairStatus ,
        t.orderWarrantyStatus ,
        t.orderServiceType ,
        t.orderRepairRank ,
        t.orderRepairType ,
        t.orderRepairProductType ,
        t.orderSource ,
        t.orderReturnLimit ,
        t.orderManualFeeLimit ,
        t.orderMaintenanceFeeLimit ,
        t.orderOldReplaceLimit ,
        t.orderBackRepairLimit ,
        t.orderFeeLimit ,
        t.orderCostLimit ,
        t.[count] ,
        t.checkProfit ,
        t.checkSalaryLossRelation ,
        t.saleAmount ,
        t.oldPartGrossProfit ,
        t.cid ,
        t.ppid ,
        t.wxkcoutputId,
        t.productId ,
        t.refundDate ,
        t.tradedate,
        t.areaid AS areaId,
        t.inUser,t.weiXiuRen
        FROM (
        SELECT
        'afterSales' orderKind ,
        '3-2' orderType ,
        s.id subId ,
        s.basket_id basketId ,
        NULL AS userType ,
        CASE WHEN ISNULL(s.stats,0)=1 THEN 1
        WHEN ISNULL(s.stats,0)=3 THEN 3
        ELSE 0
        END orderRepairStatus ,
        CASE WHEN ISNULL(s.baoxiu,0)=1 THEN 1
        WHEN ISNULL(s.baoxiu,0)=2 THEN 2
        WHEN ISNULL(s.baoxiu,0)=3 THEN 3
        ELSE 0
        END orderWarrantyStatus ,
        s.ServiceType orderServiceType ,
        CASE WHEN ISNULL(s.RepairLevel,0)=1 THEN 1
        WHEN ISNULL(s.RepairLevel,0)=2 THEN 2
        WHEN ISNULL(s.RepairLevel,0)=4 THEN 4
        END orderRepairRank ,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE WHEN ISNULL(s.ishuishou,0)=1 THEN 1
        WHEN ISNULL(s.isXcMkc,0)=1 THEN 1
        ELSE 0
        END orderRepairProductType ,
        CASE WHEN s.yuyueid>1 THEN 2
        ELSE 1
        END orderSource ,
        CASE WHEN th.id IS NOT NULL THEN 1
        ELSE 0
        END orderReturnLimit ,
        CASE WHEN rg.ppriceid IS NULL THEN 0
        ELSE 1
        END orderManualFeeLimit ,
        CASE WHEN isnull(istui,0)=1 THEN 0
        END orderMaintenanceFeeLimit ,
        CASE WHEN hh.id IS NULL THEN 0
        ELSE 1
        END orderOldReplaceLimit ,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1
        ELSE 0
        END orderBackRepairLimit ,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        0 checkProfit ,
        0 checkSalaryLossRelation ,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.brandID brandId,
        p.ismobile1 salaryProductType,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        th.dtime refundDate ,
        s.offtime tradedate,
        s.areaid,
        s.inuser inUser,s.weixiuren weiXiuRen
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=s.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        OUTER APPLY (
        SELECT TOP 1 wk.ppriceid
        FROM dbo.wxkcoutput wk WITH(NOLOCK)
        WHERE wk.wxid=s.id
        AND wk.ppriceid=0
        AND ISNULL(wk.stats,0) &lt;&gt;3
        ) rg
        OUTER APPLY (
        SELECT TOP 1 sh.id
        FROM shouhou_huishou sh WITH(NOLOCK)
        WHERE ISNULL(ishuanhuo,0)=1
        AND sh.shouhou_id=s.id
        ) hh
        OUTER APPLY (
        SELECT TOP 1 t.id,t.dtime
        FROM dbo.shouhou_tuihuan t WITH(NOLOCK)
        WHERE t.shouhou_id=s.id
        AND t.tuihuan_kind IN(1,2,3,4)
        AND ISNULL(t.isdel,0)=0
        ) th
        OUTER APPLY (
        SELECT SUM(ISNULL(CASE WHEN sh.toareaid=360 THEN sh.inprice ELSE sh.saleprice END,0) - ISNULL(sh.price,0)) AS jjmaoli
        FROM shouhou_huishou sh with(nolock)
        WHERE sh.shouhou_id=s.id
        AND ISNULL(s.wxkind,0) &lt;&gt;5
        AND ISNULL(s.issoft,0) = 0
        AND ((sh.sdtime IS NOT NULL
        AND ISNULL(sh.toareaid,0)=360)
        OR (ISNULL(sh.toareaid,0)!=360
        AND sh.saledate IS NOT NULL
        AND sh.issale=1
        AND sh.isfan=0))
        )jjml
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
        WHERE ISNULL(s.stats,0)=1
        AND ISNULL(s.isquji,0)=1
        AND s.xianshi=1
        AND s.issoft=1
        AND s.offtime BETWEEN #{calculateAfterReq.startDate} AND #{calculateAfterReq.endDate}
        AND isnull(w.part_type, 0)!=1
        AND s.areaid IN
        <foreach collection="calculateAfterReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t
        WHERE 1=1
        <if test="calculateAfterReq.orderType!=null">
            AND t.orderType = #{calculateAfterReq.orderType}
        </if>
        <if test="calculateAfterReq.cid!=null and calculateAfterReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateAfterReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateAfterReq.productId!=null and calculateAfterReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateAfterReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateAfterReq.ppId!=null and calculateAfterReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateAfterReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>

        <if test="calculateAfterReq.orderSource!=null and calculateAfterReq.orderSource.size>0">
            AND t.orderSource IN
            <foreach collection="calculateAfterReq.orderSource" item="orderSource" separator="," open="(" close=")">
                #{orderSource}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairStatus!=null and calculateAfterReq.orderRepairStatus.size>0">
            AND t.orderRepairStatus IN
            <foreach collection="calculateAfterReq.orderRepairStatus" item="orderRepairStatus" separator="," open="(" close=")">
                #{orderRepairStatus}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderWarrantyStatus!=null and calculateAfterReq.orderWarrantyStatus.size>0">
            AND t.orderWarrantyStatus IN
            <foreach collection="calculateAfterReq.orderWarrantyStatus" item="orderWarrantyStatus" separator="," open="(" close=")">
                #{orderWarrantyStatus}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairProductType!=null and calculateAfterReq.orderRepairProductType.size>0">
            AND t.orderRepairProductType IN
            <foreach collection="calculateAfterReq.orderRepairProductType" item="orderRepairProductType" separator="," open="(" close=")">
                #{orderRepairProductType}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderServiceType!=null and calculateAfterReq.orderServiceType.size>0">
            AND t.orderServiceType IN
            <foreach collection="calculateAfterReq.orderServiceType" item="orderServiceType" separator="," open="(" close=")">
                #{orderServiceType}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairType!=null and calculateAfterReq.orderRepairType.size>0">
            AND t.orderRepairType IN
            <foreach collection="calculateAfterReq.orderRepairType" item="orderRepairType" separator="," open="(" close=")">
                #{orderRepairType}
            </foreach>
        </if>

        <if test="calculateAfterReq.orderReturnLimit!=null">
            AND t.orderReturnLimit = #{calculateAfterReq.orderReturnLimit}
        </if>
        <if test="calculateAfterReq.orderOldReplaceLimit!=null">
            AND t.orderOldReplaceLimit = #{calculateAfterReq.orderOldReplaceLimit}
        </if>
        <if test="calculateAfterReq.orderManualFeeLimit!=null">
            AND t.orderManualFeeLimit = #{calculateAfterReq.orderManualFeeLimit}
        </if>
        <if test="calculateAfterReq.orderBackRepairLimit!=null">
            AND t.orderBackRepairLimit = #{calculateAfterReq.orderBackRepairLimit}
        </if>
    </select>
    <select id="listAfterOrderHardFit" parameterType="com.jiuji.oa.salary.CalculateAfterReq" resultType="com.jiuji.oa.salary.AfterOrder">
        SELECT
        t.orderKind ,
        t.orderType ,
        t.subId ,
        t.brandId,
        t.salaryProductType,
        t.basketId ,
        t.userType ,
        t.orderRepairStatus ,
        t.orderWarrantyStatus ,
        t.orderServiceType ,
        t.orderRepairRank ,
        t.orderRepairType ,
        t.orderRepairProductType ,
        t.orderSource ,
        t.orderReturnLimit ,
        t.orderManualFeeLimit ,
        t.orderMaintenanceFeeLimit ,
        t.orderOldReplaceLimit ,
        t.orderBackRepairLimit ,
        t.orderFeeLimit ,
        t.orderCostLimit ,
        t.[count] ,
        t.checkProfit ,
        t.checkSalaryLossRelation ,
        t.saleAmount ,
        t.oldPartGrossProfit ,
        t.cid ,
        t.ppid ,
        t.wxkcoutputId,
        t.productId ,
        t.refundDate ,
        t.tradedate,
        t.areaid AS areaId,
        t.inUser,t.weiXiuRen
        FROM (
        SELECT 'afterSales' orderKind,
        '3-3' orderType,
        s.id subId,
        s.basket_id basketId,
        NULL AS userType,
        CASE WHEN ISNULL(s.stats,0)=1 THEN 1 WHEN ISNULL(s.stats,0)=3 THEN 3 ELSE 0 END orderRepairStatus,
        CASE WHEN ISNULL(s.baoxiu,0)=1 THEN 1 WHEN ISNULL(s.baoxiu,0)=2 THEN 2 WHEN ISNULL(s.baoxiu,0)=3 THEN 3 ELSE 0 END orderWarrantyStatus,
        s.ServiceType orderServiceType,
        CASE WHEN ISNULL(s.RepairLevel,0)=1 THEN 1 WHEN ISNULL(s.RepairLevel,0)=2 THEN 2 WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE WHEN ISNULL(s.ishuishou,0)=1 THEN 1 WHEN ISNULL(s.isXcMkc,0)=1 THEN 1 ELSE 0 END orderRepairProductType,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        0 checkProfit ,
        0 checkSalaryLossRelation ,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.brandID brandId,
        p.ismobile1 salaryProductType,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        null refundDate ,
        0 refunded,
        s.offtime tradedate,
        s.areaid,
        s.inuser inUser,s.weixiuren weiXiuRen
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=w.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        OUTER APPLY (SELECT TOP 1 wk.ppriceid FROM dbo.wxkcoutput wk WITH(NOLOCK) WHERE wk.wxid=s.id AND wk.ppriceid=0 AND ISNULL(wk.stats,0) &lt;&gt;3) rg
        OUTER APPLY (SELECT TOP 1 sh.id FROM shouhou_huishou sh WITH(NOLOCK) WHERE ISNULL(ishuanhuo,0)=1 AND sh.shouhou_id=s.id) hh
        OUTER APPLY (SELECT TOP 1 t.id,t.dtime FROM dbo.shouhou_tuihuan t WITH(NOLOCK) WHERE t.shouhou_id=s.id AND t.tuihuan_kind IN(1,2,3,4) AND ISNULL(t.isdel,0)=0) th
        OUTER APPLY(
        SELECT SUM(ISNULL(CASE WHEN sh.toareaid=360 THEN sh.inprice ELSE sh.saleprice END,0) - ISNULL(sh.price,0)) AS jjmaoli
        FROM shouhou_huishou sh with(nolock)
        WHERE sh.shouhou_id=s.id
        AND ISNULL(s.wxkind,0) &lt;&gt;5
        AND ISNULL(s.issoft,0) = 0
        AND ((sh.sdtime IS NOT NULL
        AND ISNULL(sh.toareaid,0)=360)
        OR (ISNULL(sh.toareaid,0)!=360
        AND sh.saledate IS NOT NULL
        AND sh.issale=1
        AND sh.isfan=0))
        )jjml
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
        WHERE ISNULL(s.stats,0)=1
        AND ISNULL(s.isquji,0)=1
        AND s.xianshi=1
        AND s.issoft=0
        AND isnull(w.part_type, 0)!=1
        AND s.offtime BETWEEN #{calculateAfterReq.startDate} AND #{calculateAfterReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateAfterReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        union all


        SELECT 'afterSales' orderKind,
        '3-3' orderType,
        s.id subId,
        s.basket_id basketId,
        NULL AS userType,
        CASE WHEN ISNULL(s.stats,0)=1 THEN 1 WHEN ISNULL(s.stats,0)=3 THEN 3 ELSE 0 END orderRepairStatus,
        CASE WHEN ISNULL(s.baoxiu,0)=1 THEN 1 WHEN ISNULL(s.baoxiu,0)=2 THEN 2 WHEN ISNULL(s.baoxiu,0)=3 THEN 3 ELSE 0 END orderWarrantyStatus,
        s.ServiceType orderServiceType,
        CASE WHEN ISNULL(s.RepairLevel,0)=1 THEN 1 WHEN ISNULL(s.RepairLevel,0)=2 THEN 2 WHEN ISNULL(s.RepairLevel,0)=4 THEN 4 END orderRepairRank,
        case when qudao.id is not null then 4 else 1 end orderRepairType,
        CASE WHEN ISNULL(s.ishuishou,0)=1 THEN 1 WHEN ISNULL(s.isXcMkc,0)=1 THEN 1 ELSE 0 END orderRepairProductType,
        CASE WHEN s.yuyueid>1 THEN 2 ELSE 1 END orderSource,
        CASE WHEN th.id IS NOT NULL THEN 1 ELSE 0 END orderReturnLimit,
        CASE WHEN rg.ppriceid IS NULL THEN 0 ELSE 1 END orderManualFeeLimit,
        CASE WHEN isnull(istui,0)=1 THEN 0 END orderMaintenanceFeeLimit,
        CASE WHEN hh.id IS NULL THEN 0 ELSE 1 END orderOldReplaceLimit,
        CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END orderBackRepairLimit,
        s.feiyong orderFeeLimit ,
        s.costprice orderCostLimit ,
        1 [count] ,
        0 checkProfit ,
        0 checkSalaryLossRelation ,
        s.feiyong saleAmount ,
        ISNULL(jjml.jjmaoli,0) oldPartGrossProfit ,
        p.cid ,
        p.brandID brandId,
        p.ismobile1 salaryProductType,
        w.ppriceid ppid ,
        w.id wxkcoutputId,
        p.productid productId ,
        w.tuidtime refundDate ,
        1 refunded,
        s.offtime tradedate,
        s.areaid,
        s.inuser inUser,s.weixiuren weiXiuRen
        FROM dbo.shouhou s WITH(NOLOCK)
        LEFT JOIN dbo.wxkcoutput w with (nolock) on w.wxid = s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=w.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        OUTER APPLY (SELECT TOP 1 wk.ppriceid FROM dbo.wxkcoutput wk WITH(NOLOCK) WHERE wk.wxid=s.id AND wk.ppriceid=0 AND ISNULL(wk.stats,0) &lt;&gt;3) rg
        OUTER APPLY (SELECT TOP 1 sh.id FROM shouhou_huishou sh WITH(NOLOCK) WHERE ISNULL(ishuanhuo,0)=1 AND sh.shouhou_id=s.id) hh
        OUTER APPLY (SELECT TOP 1 t.id,t.dtime FROM dbo.shouhou_tuihuan t WITH(NOLOCK) WHERE t.shouhou_id=s.id AND t.tuihuan_kind IN(1,2,3,4) AND ISNULL(t.isdel,0)=0) th
        OUTER APPLY(
        SELECT SUM(ISNULL(CASE WHEN sh.toareaid=360 THEN sh.inprice ELSE sh.saleprice END,0) - ISNULL(sh.price,0)) AS jjmaoli
        FROM shouhou_huishou sh with(nolock)
        WHERE sh.shouhou_id=s.id
        AND ISNULL(s.wxkind,0) &lt;&gt;5
        AND ISNULL(s.issoft,0) = 0
        AND ((sh.sdtime IS NOT NULL
        AND ISNULL(sh.toareaid,0)=360)
        OR (ISNULL(sh.toareaid,0)!=360
        AND sh.saledate IS NOT NULL
        AND sh.issale=1
        AND sh.isfan=0))
        )jjml
        outer apply (select top 1 id from dbo.shouhou_qudao sq with(nolock) where sq.shouhouid=s.id) qudao
        WHERE ISNULL(s.stats,0)=1
        AND ISNULL(s.isquji,0)=1
        AND s.xianshi=1
        AND s.issoft=0
        AND isnull(w.part_type, 0)!=1
        AND s.offtime BETWEEN #{calculateAfterReq.startDate} AND #{calculateAfterReq.endDate}
        AND (CASE WHEN ISNULL(w.stats,0)=3 THEN 1 ELSE 0 END)=1
        AND s.areaid IN
        <foreach collection="calculateAfterReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        ) t
        WHERE 1=1
        <if test="calculateAfterReq.orderType!=null">
            AND t.orderType = #{calculateAfterReq.orderType}
        </if>
        <if test="calculateAfterReq.cid!=null and calculateAfterReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateAfterReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateAfterReq.productId!=null and calculateAfterReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateAfterReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateAfterReq.ppId!=null and calculateAfterReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateAfterReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>

        <if test="calculateAfterReq.orderSource!=null and calculateAfterReq.orderSource.size>0">
            AND t.orderSource IN
            <foreach collection="calculateAfterReq.orderSource" item="orderSource" separator="," open="(" close=")">
                #{orderSource}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairStatus!=null and calculateAfterReq.orderRepairStatus.size>0">
            AND t.orderRepairStatus IN
            <foreach collection="calculateAfterReq.orderRepairStatus" item="orderRepairStatus" separator="," open="(" close=")">
                #{orderRepairStatus}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderWarrantyStatus!=null and calculateAfterReq.orderWarrantyStatus.size>0">
            AND t.orderWarrantyStatus IN
            <foreach collection="calculateAfterReq.orderWarrantyStatus" item="orderWarrantyStatus" separator="," open="(" close=")">
                #{orderWarrantyStatus}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairProductType!=null and calculateAfterReq.orderRepairProductType.size>0">
            AND t.orderRepairProductType IN
            <foreach collection="calculateAfterReq.orderRepairProductType" item="orderRepairProductType" separator="," open="(" close=")">
                #{orderRepairProductType}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderServiceType!=null and calculateAfterReq.orderServiceType.size>0">
            AND t.orderServiceType IN
            <foreach collection="calculateAfterReq.orderServiceType" item="orderServiceType" separator="," open="(" close=")">
                #{orderServiceType}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderRepairType!=null and calculateAfterReq.orderRepairType.size>0">
            AND t.orderRepairType IN
            <foreach collection="calculateAfterReq.orderRepairType" item="orderRepairType" separator="," open="(" close=")">
                #{orderRepairType}
            </foreach>
        </if>
        <if test="calculateAfterReq.orderManualFeeLimit!=null">
            AND t.orderManualFeeLimit = #{calculateAfterReq.orderManualFeeLimit}
        </if>
        <if test="calculateAfterReq.orderBackRepairLimit!=null">
            AND t.orderBackRepairLimit = #{calculateAfterReq.orderBackRepairLimit}
        </if>

        <if test="calculateAfterReq.orderReturnLimit!=null">
            AND t.orderReturnLimit = #{calculateAfterReq.orderReturnLimit}
        </if>
        <if test="calculateAfterReq.orderOldReplaceLimit!=null">
            AND t.orderOldReplaceLimit = #{calculateAfterReq.orderOldReplaceLimit}
        </if>
    </select>

    <select id="listOperatorOrderOperator" parameterType="com.jiuji.oa.salary.CalculateOperatorReq" resultType="com.jiuji.oa.salary.OperatorOrder">
        SELECT
        t.isReturnOrder,
        t.orderKind,
        t.orderType,
        t.orderVType,
        t.brandId,
        t.salaryProductType,
        t.subId,
        t.userType,
        t.COUNT,
        t.saleAmount,
        t.estimatedCommission,
        t.actualCommission,
        t.agencyPoints,
        t.settlementAmount,
        t.bounty,
        t.preAbility,
        t.deductionMoney,
        t.status,
        t.returnTime,
        t.productId,
        t.ppId,
        t.cid,
        t.tradeDate,
        t.areaid AS areaId,
        0 checkSalaryLossRelation,
        0 trueSalaryLossRelation
        FROM(
        SELECT
        0 isReturnOrder,
        'operator' orderKind,
        '4-1' orderType,
        s.subtype orderVType,
        s.sub_id subId,
        NULL userType,
        b.basket_count COUNT,
        b.price2 saleAmount,
        ob.precommission estimatedCommission,
        ob.actCommission actualCommission,
        ob.agentPoints agencyPoints,
        ob.settlementMoney settlementAmount,
        ob.bounty,
        ob.preAbility,
        ob.deductionMoney,
        ob.status,
        ob.returnTime,
        p.product_id productId,
        b.ppriceid ppId,
        p.cid cid,
        p.brandID brandId,
        b.ismobile salaryProductType,
        s.tradeDate1 AS tradeDate,
        s.areaid
        FROM OperatorBasket ob WITH (nolock)
        LEFT JOIN basket b WITH (nolock) ON ob.basketId = b.basket_id
        LEFT JOIN ch999_user cc WITH (nolock) ON b.seller = cc.ch999_name
        LEFT JOIN sub s WITH (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN productinfo p WITH (nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN category c WITH (nolock) ON p.cid = c.id
        WHERE s.tradeDate1 BETWEEN #{calculateOperatorReq.startDate} AND #{calculateOperatorReq.endDate}
        AND isnull(ob.isdel, 0) = 0
        AND isnull(ob.isdel, 0) = 0 and ob.status = 1
        AND s.areaid IN
        <foreach collection="calculateOperatorReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        union all

        SELECT
        0 isReturnOrder,
        'operator' orderKind,
        '4-1' orderType,
        s.subtype orderVType,
        s.sub_id subId,
        NULL userType,
        b.basket_count COUNT,
        b.price2 saleAmount,
        ob.precommission estimatedCommission,
        ob.actCommission actualCommission,
        ob.agentPoints agencyPoints,
        ob.settlementMoney settlementAmount,
        ob.bounty,
        ob.preAbility,
        ob.deductionMoney,
        ob.status,
        ob.returnTime,
        p.product_id productId,
        b.ppriceid ppId,
        p.cid cid,
        p.brandID brandId,
        b.ismobile salaryProductType,
        s.tradeDate1 AS tradeDate,
        s.areaid
        FROM OperatorBasket ob WITH (nolock)
        LEFT JOIN basket b WITH (nolock) ON ob.basketId = b.basket_id
        LEFT JOIN ch999_user cc WITH (nolock) ON b.seller = cc.ch999_name
        LEFT JOIN sub s WITH (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN productinfo p WITH (nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN category c WITH (nolock) ON p.cid = c.id
        WHERE s.tradeDate1 BETWEEN #{calculateOperatorReq.startDate} AND #{calculateOperatorReq.endDate}
        AND isnull(ob.isdel, 0) = 0
        AND isnull(ob.isdel, 0) = 0 and ob.returnTime > s.tradeDate1
        AND s.areaid IN
        <foreach collection="calculateOperatorReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        union all

        SELECT
        1 isReturnOrder,
        'operator' orderKind,
        '4-1' orderType,
        s.subtype orderVType,
        s.sub_id subId,
        NULL userType,
        b.basket_count COUNT,
        b.price2 saleAmount,
        ob.precommission estimatedCommission,
        ob.actCommission actualCommission,
        ob.agentPoints agencyPoints,
        ob.settlementMoney settlementAmount,
        ob.bounty,
        ob.preAbility,
        ob.deductionMoney,
        ob.status,
        ob.returnTime,
        p.product_id productId,
        b.ppriceid ppId,
        p.cid cid,
        p.brandID brandId,
        b.ismobile salaryProductType,
        s.tradeDate1 AS tradeDate,
        s.areaid
        FROM OperatorBasket ob WITH (nolock)
        LEFT JOIN basket b WITH (nolock) ON ob.basketId = b.basket_id
        LEFT JOIN ch999_user cc WITH (nolock) ON b.seller = cc.ch999_name
        LEFT JOIN sub s WITH (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN productinfo p WITH (nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.brand brand WITH(NOLOCK) ON p.brandID = brand.id
        LEFT JOIN category c WITH (nolock) ON p.cid = c.id
        WHERE ob.returnTime BETWEEN #{calculateOperatorReq.startDate} AND #{calculateOperatorReq.endDate}
        AND isnull(ob.isdel, 0) = 0
        and s.tradeDate1 is not NULL and ob.returnTime > s.tradeDate1
        AND s.areaid IN
        <foreach collection="calculateOperatorReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t
        WHERE 1=1
        <if test="calculateOperatorReq.orderType!=null">
            AND t.orderType = #{calculateOperatorReq.orderType}
        </if>
        <if test="calculateOperatorReq.cid!=null and calculateOperatorReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateOperatorReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateOperatorReq.productId!=null and calculateOperatorReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateOperatorReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateOperatorReq.ppId!=null and calculateOperatorReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateOperatorReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
    </select>

    <select id="listUserCompletionRate" resultType="com.jiuji.oa.salary.UserCompletionRate">
        SELECT u.ch999_id,u.ch999_name,CASE WHEN t.sjTask > 0 THEN CAST(ROUND(t1.大件销量 * 100.0 / t.sjTask,2) AS DECIMAL(18,2)) ELSE 100 END mobileCompletionRateUser
             ,CASE WHEN t.pjTask > 0 THEN CAST(ROUND(t1.手机配件利润额 * 100.0 / t.pjTask,2) AS DECIMAL(18,2)) ELSE 100 END mobileSmallProfitCompletionRateUser
             ,CASE WHEN t.zqpjTask > 0 THEN CAST(ROUND(t1.智能产品利润额 * 100.0 / t.zqpjTask,2) AS DECIMAL(18,2)) ELSE 100 END intelligentProfitCompletionRateUser
             ,CASE WHEN t.fwTask > 0 THEN CAST(ROUND(t1.服务销售额 * 100.0 / t.fwTask,2) AS DECIMAL(18,2)) ELSE 100 END serviceCompletionRateUser
             ,CASE WHEN t1.大件销量 > 0 THEN CAST(ROUND(t1.服务销售量 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 100 END serviceFollowUpRateUser
             ,CASE WHEN t.OldTask > 0 THEN CAST(ROUND(t1.回收量1 * 100.0 / t.OldTask,2) AS DECIMAL(18,2)) ELSE 100 END returnCompletionRateUser
             ,CASE WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收量,2) AS DECIMAL(18,2)) ELSE 0 END returnDifferenceRateUser
             ,CASE WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收总量,2) AS DECIMAL(18,2)) ELSE 0 END returnTotalDifferenceRateUser
             ,CASE WHEN t1.大件销量 > 0 THEN CAST(ROUND(t1.回收量1 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 0 END returnFollowUpRateUser
             ,CASE WHEN t1.大件销量 > 0 THEN CAST(ROUND(t1.回收总量1 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 0 END returnTotalFollowUpRateUser
             ,CASE WHEN t.weixiuTask > 0 THEN CAST(ROUND(t1.维修毛利 * 100.0 / t.weixiuTask,2) AS DECIMAL(18,2)) ELSE 100 END afterRepairCompletionRateUser
        FROM
            (
                SELECT b.ch999_name,SUM(CASE WHEN b.kinds = 1 THEN b.val ELSE 0 END) 大件销量
                     ,SUM(CASE WHEN b.kinds = 2 THEN b.val ELSE 0 END) 手机配件利润额
                     ,SUM(CASE WHEN b.kinds = 3 THEN b.val ELSE 0 END) 智能产品利润额
                     ,SUM(CASE WHEN b.kinds = 4 THEN b.val ELSE 0 END) 服务销售额
                     ,SUM(CASE WHEN b.kinds = 4 THEN b.val2 ELSE 0 END) 服务销售量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val ELSE 0 END) 回收总量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val2 ELSE 0 END) 回收差异量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val3 ELSE 0 END) 回收量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val4 ELSE 0 END) 回收量1
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val5 ELSE 0 END) 回收总量1
                     ,SUM(CASE WHEN b.kinds = 7 THEN b.val3 ELSE 0 END) 跟机率回收量
                     ,SUM(CASE WHEN b.kinds = 6 THEN b.val ELSE 0 END) 维修毛利
                FROM (
                         SELECT b.seller ch999_name,s.areaid,SUM(b.basket_count * 1.0) val,NULL val2,NULL val3,NULL val4, NULL val5, 1 kinds FROM sub s with(nolock)
                        left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('2,20,22,201,21') f where f.ID = p.cid)
                         GROUP BY b.seller,s.areaid

                         UNION ALL

                         SELECT b.seller ch999_name,s.areaid,SUM((CASE WHEN b.ismobile = 1 THEN 1 ELSE b.basket_count END) *
                             (case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
                             when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
                             when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
                             else b.price2 - isnull(b.inprice,b.price2) END)
                             ) val,NULL val2,NULL val3,NULL val4,NULL val5,2 kinds
                         FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                             left join product_mkc k with(nolock) on b.basket_id = k.basket_id
                             left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('4,607') f where f.ID = p.cid)
                         GROUP BY b.seller,s.areaid

                         UNION ALL

                         SELECT b.seller ch999_name,s.areaid,SUM((CASE WHEN b.ismobile = 1 THEN 1 ELSE b.basket_count END) *
                             (case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
                             when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
                             when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
                             else b.price2 - isnull(b.inprice,b.price2) END)
                             ) val,NULL val2,NULL val3,NULL val4,NULL val5,3 kinds
                         FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                             left join product_mkc k with(nolock) on b.basket_id = k.basket_id
                             left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('32,156,113,99,100,144,194,148,321,509,599,596,602,597') f where f.ID = p.cid)
                         GROUP BY b.seller,s.areaid

                         UNION ALL

                         SELECT b.seller ch999_name,s.areaid,SUM(b.price) val,SUM(b.basket_count)  val2,NULL val3,NULL val4,NULL val5,4 kinds  FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 AND b.ismobile = 0 and isnull(b.type,0) &lt;&gt; 1
                           AND exists(select 1 from productinfo p with(nolock) where b.ppriceid = p.ppriceid and p.cid in(50))
                           AND not exists(select 1 from dbo.cardLogs c with(nolock),dbo.NumberCard n with(nolock) where c.cardid = n.ID and n.isNoProfit = 1 and c.sub_id = b.sub_id)
                         GROUP BY b.seller,s.areaid

                         UNION ALL

                         SELECT b.inuser ch999_name,s.areaid,COUNT(1) val
                                 ,SUM(CASE WHEN ISNULL(k.diffopt,'') &lt;&gt; '' THEN 1 ELSE 0 END) val2
                                 ,SUM(CASE WHEN ISNULL(b.price,0) &gt;= 21 THEN 1 ELSE 0 END) val3
                                 ,NULL val4,NULL val5
                                 ,5 kinds
                         FROM dbo.recover_sub s with(nolock)
                             left join dbo.recover_basket b with(nolock) on s.sub_id = b.sub_id
                             LEFT JOIN dbo.recover_mkc k with(nolock) ON k.from_basket_id = b.id AND ISNULL(k.ishouhou,0) = 0
                             left join dbo.recover_marketSubInfo rm with(nolock) on k.to_basket_id = rm.basket_id
                             left join dbo.recover_marketInfo rs with(nolock) on rm.sub_id = rs.sub_id
                         where s.sub_check in (3) AND ISNULL(b.isdel,0) = 0 and rs.tradeDate1 between #{startDate} and #{endDate}
                           and isnull(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
                         GROUP BY b.inuser,s.areaid

                         UNION ALL

                         SELECT b.inuser ch999_name,s.areaid,NULL val
                                 ,NULL val2
                                 ,NULL val3
                                 ,SUM(CASE WHEN ISNULL(b.price,0) &gt;= 21 THEN 1 ELSE 0 END) val4
                                 ,COUNT(1) val5
                                 ,5 kinds
                         FROM dbo.recover_sub s with(nolock)
                             left join dbo.recover_basket b with(nolock) on s.sub_id = b.sub_id
                             LEFT JOIN dbo.recover_mkc k with(nolock) ON k.from_basket_id = b.id AND ISNULL(k.ishouhou,0) = 0
                             left join dbo.recover_marketSubInfo rm with(nolock) on k.to_basket_id = rm.basket_id
                             left join dbo.recover_marketInfo rs with(nolock) on rm.sub_id = rs.sub_id
                         where s.sub_check in (3) AND ISNULL(b.isdel,0) = 0 and s.pay_time between #{startDate} and #{endDate}
                           AND isnull(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
                         GROUP BY b.inuser,s.areaid

                         UNION ALL

                         select s.inuser ch999_name,s.areaid,SUM(ISNULL(k.wxtongjitotal,0) - k.inprice) val,NULL val2,NULL val3,NULL val4,NULL val5,6 kinds from shouhou s WITH(NOLOCK)
                             INNER JOIN dbo.wxkcoutput k  WITH(NOLOCK) ON s.id = k.wxid
                             LEFT join dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=k.ppriceid
                         WHERE ISNULL(s.stats, 0)= 1 AND ISNULL(s.isquji, 0)= 1 AND ISNULL(s.baoxiu, 0)!= 1 AND ISNULL(xianshi,0)= 1 AND ISNULL(s.istui, 0)= 0
                           AND ISNULL(s.issoft, 0)= 0 AND(ISNULL(s.ServiceType, 0) = 0 OR(ISNULL(s.ServiceType, 0) != 0 AND ISNULL(k.wxtongjitotal,0) &gt; 0))
                           and not exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo, 0) = 1 and sh.shouhou_id = s.id AND sh.wxkcid = k.id) AND (p.cid!=31 OR s.wxkind!=5)
                           and s.offtime between #{startDate} and #{endDate} AND k.stats != 3
                         GROUP BY s.inuser,s.areaid
                     ) b
                GROUP BY b.ch999_name
            ) t1
                INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = t1.ch999_name
            LEFT JOIN dbo.SalesTask t WITH(NOLOCK) ON t.type_ = 3 AND t.TaskDate = CONVERT(varchar(6), #{startDate}, 112) AND t.fieldVal = CAST(u.ch999_id AS VARCHAR(20))
    </select>
    <select id="listAreaCompletionRate" resultType="com.jiuji.oa.salary.AreaCompletionRate">
        SELECT t1.areaid,CASE WHEN t.sjTask &gt; 0 THEN CAST(ROUND(t1.大件销量 * 100.0 / t.sjTask,2) AS DECIMAL(18,2)) ELSE 100 END mobileCompletionRateArea
             ,CASE WHEN t.pjTask &gt; 0 THEN CAST(ROUND(t1.手机配件利润额 * 100.0 / t.pjTask,2) AS DECIMAL(18,2)) ELSE 100 END mobileSmallProfitCompletionRateArea
             ,CASE WHEN t.zqpjTask &gt; 0 THEN CAST(ROUND(t1.智能产品利润额 * 100.0 / t.zqpjTask,2) AS DECIMAL(18,2)) ELSE 100 END intelligentProfitCompletionRateArea
             ,CASE WHEN t.fwTask &gt; 0 THEN CAST(ROUND(t1.服务销售额 * 100.0 / t.fwTask,2) AS DECIMAL(18,2)) ELSE 100 END serviceCompletionRateArea
             ,CASE WHEN t1.大件销量 &gt; 0 THEN CAST(ROUND(t1.服务销售量 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 100 END serviceFollowUpRateArea
             ,CASE WHEN t.OldTask &gt; 0 THEN CAST(ROUND(t1.回收量1 * 100.0 / t.OldTask,2) AS DECIMAL(18,2)) ELSE 100 END returnCompletionRateArea
             ,CASE WHEN t1.回收量 &gt; 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收量,2) AS DECIMAL(18,2)) ELSE 0 END returnDifferenceRateArea
             ,CASE WHEN t1.回收量 &gt; 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收总量,2) AS DECIMAL(18,2)) ELSE 0 END returnTotalDifferenceRateArea
             ,CASE WHEN t1.大件销量 &gt; 0 THEN CAST(ROUND(t1.回收量1 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 0 END returnFollowUpRateArea
             ,CASE WHEN t1.大件销量 &gt; 0 THEN CAST(ROUND(t1.回收总量1 * 100.0 / t1.大件销量,2) AS DECIMAL(18,2)) ELSE 0 END returnTotalFollowUpRateArea
             ,CASE WHEN t.weixiuTask &gt; 0 THEN CAST(ROUND(t1.维修毛利 * 100.0 / t.weixiuTask,2) AS DECIMAL(18,2)) ELSE 100 END afterRepairCompletionRateArea
        FROM
            (
                SELECT b.areaid,SUM(CASE WHEN b.kinds = 1 THEN b.val ELSE 0 END) 大件销量
                     ,SUM(CASE WHEN b.kinds = 2 THEN b.val ELSE 0 END) 手机配件利润额
                     ,SUM(CASE WHEN b.kinds = 3 THEN b.val ELSE 0 END) 智能产品利润额
                     ,SUM(CASE WHEN b.kinds = 4 THEN b.val ELSE 0 END) 服务销售额
                     ,SUM(CASE WHEN b.kinds = 4 THEN b.val2 ELSE 0 END) 服务销售量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val ELSE 0 END) 回收总量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val2 ELSE 0 END) 回收差异量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val3 ELSE 0 END) 回收量
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val4 ELSE 0 END) 回收量1
                     ,SUM(CASE WHEN b.kinds = 5 THEN b.val5 ELSE 0 END) 回收总量1
                     ,SUM(CASE WHEN b.kinds = 6 THEN b.val ELSE 0 END) 维修毛利
                FROM (
                         SELECT s.areaid,SUM(b.basket_count * 1.0) val,NULL val2,NULL val3,NULL val4,NULL val5,1 kinds FROM sub s with(nolock)
                    left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and  #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('2,20,22,201,21') f where f.ID = p.cid)
                         GROUP BY s.areaid

                         UNION ALL

                         SELECT s.areaid,SUM((CASE WHEN b.ismobile = 1 THEN 1 ELSE b.basket_count END) *
                             (case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
                             when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
                             when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
                             else b.price2 - isnull(b.inprice,b.price2) END)
                             ) val,NULL val2,NULL val3,NULL val4,NULL val5,2 kinds
                         FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                             left join product_mkc k with(nolock) on b.basket_id = k.basket_id
                             left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and  #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('4,607') f where f.ID = p.cid)
                         GROUP BY s.areaid

                         UNION ALL

                         SELECT s.areaid,SUM((CASE WHEN b.ismobile = 1 THEN 1 ELSE b.basket_count END) *
                             (case when b.ismobile = 1 and z.mkc_id is not null and b.price &lt; isnull(k.staticPrice,b.price2) then 0
                             when b.ismobile = 1 then b.price2 - isnull(k.staticPrice,b.price2)
                             when b.ismobile = 0 and isnull(b.type,0) in (1,13,29,22) then 0
                             else b.price2 - isnull(b.inprice,b.price2) END)
                             ) val,NULL val2,NULL val3,NULL val4,NULL val5,3 kinds
                         FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                             left join product_mkc k with(nolock) on b.basket_id = k.basket_id
                             left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and  #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 and isnull(b.type,0)&lt;&gt;22
                           AND exists(select 1 from dbo.f_category_children('32,156,113,99,100,144,194,148,321,509,599,596,602,597') f where f.ID = p.cid)
                         GROUP BY s.areaid

                         UNION ALL

                         SELECT s.areaid,SUM(b.price) val,SUM(b.basket_count)  val2,NULL val3,NULL val4,NULL val5,4 kinds  FROM sub s with(nolock)
                             left join basket b with(nolock) on s.sub_id = b.sub_id
                             left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
                         where s.sub_check in (3) and s.tradeDate1 between #{startDate} and  #{endDate}
                           and b.ischu = 1 and isnull(b.isdel,0) = 0 AND b.ismobile = 0 and isnull(b.type,0) &lt;&gt; 1
                           AND exists(select 1 from productinfo p with(nolock) where b.ppriceid = p.ppriceid and p.cid in(50))
                           AND not exists(select 1 from dbo.cardLogs c with(nolock),dbo.NumberCard n with(nolock) where c.cardid = n.ID and n.isNoProfit = 1 and c.sub_id = b.sub_id)
                         GROUP BY  s.areaid

                         UNION ALL

                         SELECT  s.areaid,COUNT(1) val
                                 ,SUM(CASE WHEN ISNULL(k.diffopt,'') &lt;&gt; '' THEN  1 ELSE 0 END) val2
                                 ,SUM(CASE WHEN ISNULL(b.price,0) &gt;= 21 THEN 1 ELSE 0 END) val3
                                 ,NULL val4
                                 ,NULL val5,5 kinds
                         FROM dbo.recover_sub s with(nolock)
                             left join dbo.recover_basket b with(nolock) on s.sub_id = b.sub_id
                             LEFT JOIN dbo.recover_mkc k with(nolock) ON k.from_basket_id = b.id AND ISNULL(k.ishouhou,0) = 0
                             left join dbo.recover_marketSubInfo rm with(nolock) on k.to_basket_id = rm.basket_id
                             left join dbo.recover_marketInfo rs with(nolock) on rm.sub_id = rs.sub_id
                         where s.sub_check in (3) AND ISNULL(b.isdel,0) = 0 and rs.tradeDate1 between #{startDate} and  #{endDate}
                           AND isnull(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
                         GROUP BY s.areaid

                         UNION ALL

                         SELECT s.areaid,NULL val
                                 ,NULL val2
                                 ,NULL val3
                                 ,SUM(CASE WHEN ISNULL(b.price,0) &gt;= 21 THEN 1 ELSE 0 END) val4
                                 ,count(1) val5
                                 ,5 kinds
                         FROM dbo.recover_sub s with(nolock)
                             left join dbo.recover_basket b with(nolock) on s.sub_id = b.sub_id
                             LEFT JOIN dbo.recover_mkc k with(nolock) ON k.from_basket_id = b.id AND ISNULL(k.ishouhou,0) = 0
                             left join dbo.recover_marketSubInfo rm with(nolock) on k.to_basket_id = rm.basket_id
                             left join dbo.recover_marketInfo rs with(nolock) on rm.sub_id = rs.sub_id
                         where s.sub_check in (3) AND ISNULL(b.isdel,0) = 0 and s.pay_time between #{startDate} and  #{endDate}
                           AND isnull(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
                         GROUP BY s.areaid

                         UNION ALL

                         select s.areaid
                                 ,SUM(ISNULL(k.wxtongjitotal,0) - k.inprice) val,NULL val2,NULL val3,NULL val4,NULL val5
                                 ,6 kinds
                         from shouhou s WITH(NOLOCK)
                             INNER JOIN dbo.wxkcoutput k  WITH(NOLOCK) ON s.id = k.wxid
                             LEFT join dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=k.ppriceid
                         WHERE ISNULL(s.stats, 0)= 1 AND ISNULL(s.isquji, 0)= 1 AND ISNULL(s.baoxiu, 0)!= 1 AND ISNULL(xianshi,0)= 1 AND ISNULL(s.istui, 0)= 0
                           AND ISNULL(s.issoft, 0)= 0 AND(ISNULL(s.ServiceType, 0) = 0 OR(ISNULL(s.ServiceType, 0) != 0 AND ISNULL(k.wxtongjitotal,0) &gt; 0))
                           and not exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo, 0) = 1 and sh.shouhou_id = s.id AND sh.wxkcid = k.id) AND (p.cid!=31 OR s.wxkind!=5)
                           and s.offtime between #{startDate} and  #{endDate} AND k.stats != 3
                         GROUP BY s.areaid
                     ) b
                GROUP BY b.areaid
            ) t1
                LEFT JOIN dbo.SalesTask t WITH(NOLOCK) ON t.type_ = 2 AND t.TaskDate = CONVERT(varchar(6), #{startDate}, 112) AND t.fieldVal = CAST(t1.areaid AS VARCHAR(20))
    </select>
    <select id="getNewOrderPayType" resultType="com.jiuji.oa.oacore.salary.bo.dto.PayType">
        SELECT y.sub_id,o.type_,y.hejim FROM dbo.shouyin_other o with(nolock)
        INNER JOIN dbo.shouying y with(nolock) ON y.id = o.shouyinid
        WHERE
        <choose>
            <when test='orderType!=null and orderType=="1"'>
                y.shouying_type IN ('订金','交易')
            </when>
            <otherwise>
                y.shouying_type IN ('订金2','交易2')
            </otherwise>
        </choose>
        <if test="subIds.lenth>0">
            AND y.sub_id IN
            <foreach collection="subIds" close=")" open="(" index="index" item="subId" separator=",">
                #{subId}
            </foreach>
        </if>
    </select>
    <select id="getCheckProfit" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.sysConfig with(nolock) WHERE ISNULL(isdel,0) = 0 AND xtenant = #{xtenant} AND code = 24 AND value = '1'
    </select>

    <select id="newAndBestList" resultType="com.jiuji.oa.oacore.salary.bo.dto.SaleOrderPayType">
        SELECT
        y.sub_id AS subId,
        o.type_ AS orderPayType
        FROM dbo.shouyin_other o with(nolock)
        INNER JOIN dbo.shouying y with(nolock) ON y.id = o.shouyinid
        WHERE y.shouying_type IN ('订金','交易')
        AND y.sub_id IN
        <foreach collection="newAndBestSaleOrders" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
        UNION
        SELECT s2.sub_id AS subId,o.type_ AS orderPayType
        FROM dbo.shouyin_other o with(nolock)
        INNER JOIN dbo.shouying y with(nolock) ON y.id = o.shouyinid
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = y.sub_id
        INNER JOIN dbo.sub s2 with(nolock) ON s.sub_id = s2.subPID
        WHERE  y.shouying_type IN ('订金','交易')  AND s2.sub_id IN
        <foreach collection="newAndBestSaleOrders" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
        UNION
        SELECT s.sub_id AS subId,o.type_ AS orderPayType
        FROM dbo.shouyin_other o with(nolock)
        INNER JOIN dbo.shouying y with(nolock) ON y.id = o.shouyinid
        INNER JOIN dbo.sub s with(nolock) ON s.subPID = y.sub_id
        WHERE  y.shouying_type IN ('订金','交易')  AND s.sub_id in
        <foreach collection="newAndBestSaleOrders" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="goodList" resultType="com.jiuji.oa.oacore.salary.bo.dto.SaleOrderPayType">
        SELECT
        y.sub_id AS subId,
        o.type_ AS orderPayType
        FROM dbo.shouyin_other o with(nolock)
        INNER JOIN dbo.shouying y with(nolock) ON y.id = o.shouyinid
        WHERE y.shouying_type IN ('订金2','交易2')
        AND y.sub_id IN
        <foreach collection="goodSaleOrders" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listRacing" resultType="com.jiuji.oa.oacore.salary.bo.dto.RacingDataDto">
        select otherPrice amount,
               type salaryName,
               categoryName salaryCategoryName,
               categoryId   salaryCategoryId,
               ChildCategoryId salaryChildCategoryId,
               childCategoryName salaryChildCategoryName
        from dbo.ch999Racing with(nolock) where ch999_id=#{ch999Id} and otherPrice!=0 and racingPrice=0 and month between #{month} and #{endMonth}
    </select>

    <select id="getBrands" resultType="com.jiuji.oa.salary.BrandRes$BrandDetailRes">
        SELECT distinct b.id as value, b.name as label,p.ismobile,p.cid
        FROM product p WITH (NOLOCK)
            INNER JOIN brand b WITH (NOLOCK) ON p.brandID = b.id
    </select>

    <select id="getBrandsV1" resultType="com.jiuji.oa.salary.BrandRes$BrandDetailRes">
        SELECT b.id as value,
               b.name as label,
               category = (SELECT STUFF((SELECT ',' + CAST(bc.categoryID AS VARCHAR)
                              FROM brandCategory bc (NOLOCK)
                              where bc.brandID = b.id
                              for xml path('')), 1, 1, ''))
        FROM brand b
        WITH (NOLOCK)
    </select>

    <select id="listReturnOrderProfit" resultType="com.jiuji.oa.salary.ReturnOrder">
        SELECT
        t.basketId,
        t.brandID brandId,
        t.cid,
        t.COUNT,
        t.orderDelivery,
        t.orderKind,
        t.orderPayType,
        t.orderType,
        t.goodProductDisplay,
        t.ppId,
        t.orderVType,
        t.productId,
        t.salaryProductType,
        t.saleAmount,
        t.returnAmount,
        t.subId,
        t.tradeDate,
        t.trueProfit,
        t.salaryLossRelation AS trueSalaryLossRelation,
        0 checkSalaryLossRelation,
        t.difference,
        t.userType,
        t.refundDate,
        t.refunded,
        t.areaid AS areaId
        FROM
        (
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        p.ppriceid ppId,
        CASE
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 0 THEN
        1
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 1 THEN
        2
        WHEN isnull( s.recover_subType, 0 ) = 1 THEN
        3
        WHEN isnull( s.recover_subType, 0 ) = 2 THEN
        4
        END orderVType,
        p.productid productId,
        rb.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                ISNULL( rb.price2, rb.price ) saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        rs.sub_id subId,
        rs.tradeDate1 tradeDate,
        ISNULL( rb.price2, rb.price ) - k.inprice - ISNULL( k.addprice, 0 ) trueProfit,
        CASE
        WHEN ISNULL( rb.price2, rb.price ) - k.inprice - ISNULL( k.addprice, 0 ) &lt; 0 THEN
        1 ELSE 0
        END salaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        s.areaid,
        NULL refundDate,
        0 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        WHERE
        rs.sub_check = 3
        AND ISNULL(rb.isdel, 0 ) = 0
        AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        UNION ALL
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        '2-3' orderType,
        CASE WHEN isnull(rs.saleType, 0)=0 THEN 1 ELSE 0 END goodProductDisplay,
        p.ppriceid ppId,
        CASE
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 0 THEN
        1
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 1 THEN
        2
        WHEN isnull( s.recover_subType, 0 ) = 1 THEN
        3
        WHEN isnull( s.recover_subType, 0 ) = 2 THEN
        4
        END orderVType,
        p.productid productId,
        rb.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                ISNULL( rb.price2, rb.price ) saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        rs.sub_id subId,
        rs.tradeDate1 tradeDate,
        ISNULL( rb.price2, rb.price ) - k.inprice - ISNULL( k.addprice, 0 ) trueProfit,
        CASE
        WHEN ISNULL( rb.price2, rb.price ) - k.inprice - ISNULL( k.addprice, 0 ) &lt; 0 THEN
        1 ELSE 0
        END salaryLossRelation,
        CASE WHEN isnull(k.diffopt, '')='' THEN 0 ELSE 1 END difference,
        NULL userType,
        rs.areaid,
        rs.returnDate refundDate,
        1 refunded
        FROM dbo.recover_marketInfo rs with(nolock)
        INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        WHERE
        rs.sub_check = 9
        AND ISNULL(rb.isdel, 0 ) = 0
        AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t
        WHERE 1 = 1
        <if test="calculateReturnReq.orderType!=null">
            AND t.orderType = #{calculateReturnReq.orderType}
        </if>
        <if test="calculateReturnReq.cid!=null and calculateReturnReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateReturnReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateReturnReq.productId!=null and calculateReturnReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateReturnReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateReturnReq.ppId!=null and calculateReturnReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateReturnReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderVType!=null and calculateReturnReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateReturnReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderPayType!=null and calculateReturnReq.orderPayType.size>0">
            AND t.orderPayType IN
            <foreach collection="calculateReturnReq.orderPayType" item="orderPayTypeId" separator="," open="(" close=")">
                #{orderPayTypeId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderDelivery!=null and calculateReturnReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateReturnReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
    </select>

    <sql id="returnPrice">
        case
        when isnull(b.price, 0) - isnull(b.price1, 0) - isnull(b.addCodePrice, 0) - isnull(b.equity_addCodePrice, 0) - isnull(b.sale_out_lp_add_price, 0) >= 0
        then
        isnull(b.price1, 0) + isnull(b.addCodePrice, 0) + isnull(b.sale_out_lp_add_price, 0) + isnull(b.equity_addCodePrice, 0)
        else isnull(b.price, 0)
        end
    </sql>

    <select id="getRecoverDiffRateDto" resultType="com.jiuji.oa.oacore.salary.bo.dto.RecoverDiffRateDto">
        select tt.ch999_id,
        IIF(tt.saleCount &gt; 0, CAST(ROUND(tt.diffCount * 100.0 / tt.saleCount, 2) AS DECIMAL(18, 2)), 100) returnDifferenceRateUser
        from (
        select u.ch999_id,
        count(DISTINCT si3.sub_id)                                    as saleCount,
        sum(case when isnull(k3.diffopt, '') != '' then 1 else 0 end) as diffCount
        from dbo.recover_sub rs3 with (nolock)
        inner join dbo.recover_basket rb3 with (nolock) on rb3.sub_id = rs3.sub_id
        inner join dbo.recover_mkc k3 with (nolock) on k3.from_basket_id = rb3.id
        inner join dbo.recover_marketSubInfo si3 with (nolock) on si3.basket_id = k3.to_basket_id
        inner join dbo.recover_marketInfo i3 with (nolock) on i3.sub_id = si3.sub_id
        inner join dbo.ch999_user u with (nolock) on
        <include refid="getRecoverDiffRateDtoChoose"></include>= u.ch999_name
        where i3.tradeDate1 between #{startDate} and #{endDate}
        and isnull(i3.sub_check, 0) in (3, 9)
        and i3.sub_to != '回收机退回渠道'
        and isnull(rb3.isdel, 0) = 0
        and isnull(k3.ishouhou, 0) = 0
        and isnull(si3.isdel, 0) = 0
        group by <include refid="getRecoverDiffRateDtoChoose"></include>
        , u.ch999_id
        ) tt
    </select>
    <sql id="getRecoverDiffRateDtoChoose">
        <choose>
            <when test="isRecoverTeacher == true">
                rb3.checkUser
            </when>
            <otherwise>
                rb3.inuser
            </otherwise>
        </choose>
    </sql>

    <select id="getAfterOrderTrueProfitVo" resultType="com.jiuji.oa.salary.AfterOrderTrueProfitVo">
        select subId,userType,sum(trueProfitTemp) trueProfit,wxkcoutputId
        from (
                 SELECT s.id as                        subId,
                        (s.维修配件费用 - s.维修配件成本 - s.回收抵扣金额 - s.维修配件优惠费用) +
                        (s.手工费用 - s.手工费用成本 - s.手工优惠费用) trueProfitTemp,
                        1 userType,
                        s.wxkcoutputId
                 from (
                          SELECT 1                                                                 type,
                                 s.id,w.id wxkcoutputId,
                                 isnull(case
                                            when w.ppriceid > 0 and w.service_type > 0 then w.price1
                                            when w.ppriceid > 0 then w.price
                                            else 0.0 end, 0.0)                                     维修配件费用,
                                 isnull((case
                                             when w.ppriceid > 0 then 0.0
                                             when w.service_type > 0 then w.price1
                                             else w.price end),
                                        0.0)                                                       手工费用,
                                 isnull(cast(hs.price AS DECIMAL(16, 2)), 0.0)                     回收抵扣金额,
                                 isnull(case when w.ppriceid > 0 then w.inprice else 0.0 end, 0.0) 维修配件成本,
                                 isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
                                 s.offtime                                                         办理时间,
                                 isnull(case
                                            when w.ppriceid > 0 then
                                                    (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                                                    +
                                                    isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
                                        0.0)                                                       维修配件优惠费用,
                                 isnull(case
                                            when w.ppriceid > 0 then 0
                                            else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                                                +
                                                 isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
                                        0.0)                                                       手工优惠费用
                          FROM dbo.shouhou s with (nolock)
        inner join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id
                              left join dbo.shouhou_huishou hs
                          with (nolock)
                          on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.inuser=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and s.offtime BETWEEN #{startDate}
                            AND #{endDate}

                          UNION all

                          SELECT 2 type,
                              s.id,w.id ppriceid,
                              0.0 维修配件费用,
                              0.0 手工费用,
                              0.0 回收抵扣金额,
                              -isnull(case when w.ppriceid > 0 and isnull(w.tui_status, 0) != 1 then w.inprice else 0.0 end,
                              0.0) 维修配件成本,
                              -isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
                              w.tuidtime 办理时间,
                              0 维修配件优惠费用,
                              0 手工优惠费用
                          FROM dbo.shouhou s
                          with (nolock)
                              left join dbo.productinfo p (nolock)
                          on p.ppriceid=s.ppriceid
                              inner join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id and w.stats = 3
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.inuser=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and w.tuidtime BETWEEN #{startDate}
                            AND #{endDate}

                          UNION all

                          SELECT 3 type,
                              s.id,w.id ppriceid,
                              -isnull(case
                              when w.ppriceid > 0 and w.service_type > 0 then w.price1
                              when w.ppriceid > 0 then w.price
                              else 0.0 end, 0.0) 维修配件费用,
                              -isnull(case when w.ppriceid > 0 then 0.0 when w.service_type > 0 then w.price1 else w.price end,
                              0.0) 手工费用,
                              -isnull(cast (hs.price AS DECIMAL (16, 2)), 0.00) 回收抵扣金额,
                              0 维修配件成本,
                              0 手工费用成本,
                              st.check3dtime 办理时间,
                              -isnull(case
                              when w.ppriceid > 0 then
                              (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                              + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00))
                              else 0 end, 0.0) 维修配件优惠费用,
                              -isnull(case
                              when w.ppriceid > 0 then 0
                              else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                              + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00)) end,
                              0.0) 手工优惠费用
                          FROM dbo.shouhou s
                          with (nolock)
                              inner join (select st.id,
                              st.shouhou_id,
                              st.dtime,
                              st.check3dtime,
                              lag(st.dtime, 1) over (PARTITION by st.shouhou_id order by st.dtime asc) preStTime
                              from dbo.shouhou_tuihuan st with (nolock)
                              where isnull(st.isdel, 0) = 0
                              and st.check3dtime BETWEEN #{startDate} AND #{endDate}
                              and st.tuihuan_kind in (5, 11)
                              and st.check3 = 1) st
                          on st.shouhou_id = s.id and st.check3dtime > s.offtime
                              left join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id and w.stats = 3 and (w.fk_tuihuan_id = st.id or w.tuidtime >= isnull(st.preStTime,s.offtime) and (w.tuidtime &lt; st.dtime or ABS(DATEDIFF(minute, cast(w.tuidtime as datetime),st.dtime)) &lt;= 1))
                              left join dbo.shouhou_huishou hs
                          with (nolock)
                          on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.inuser=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and w.id is not null
                      ) s

                 union all

                 SELECT s.id as                        subId,
                        (s.维修配件费用 - s.维修配件成本 - s.回收抵扣金额 - s.维修配件优惠费用) +
                        (s.手工费用 - s.手工费用成本 - s.手工优惠费用) trueProfitTemp,
                        2 userType,
                        s.wxkcoutputId
                 from (
                          SELECT 1                                                                 type,
                                 s.id,w.id wxkcoutputId,
                                 isnull(case
                                            when w.ppriceid > 0 and w.service_type > 0 then w.price1
                                            when w.ppriceid > 0 then w.price
                                            else 0.0 end, 0.0)                                     维修配件费用,
                                 isnull((case
                                             when w.ppriceid > 0 then 0.0
                                             when w.service_type > 0 then w.price1
                                             else w.price end),
                                        0.0)                                                       手工费用,
                                 isnull(cast(hs.price AS DECIMAL(16, 2)), 0.0)                     回收抵扣金额,
                                 isnull(case when w.ppriceid > 0 then w.inprice else 0.0 end, 0.0) 维修配件成本,
                                 isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
                                 s.offtime                                                         办理时间,
                                 isnull(case
                                            when w.ppriceid > 0 then
                                                    (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                                                    +
                                                    isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
                                        0.0)                                                       维修配件优惠费用,
                                 isnull(case
                                            when w.ppriceid > 0 then 0
                                            else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                                                +
                                                 isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
                                        0.0)                                                       手工优惠费用
                          FROM dbo.shouhou s with (nolock)
        inner join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id
                              left join dbo.shouhou_huishou hs
                          with (nolock)
                          on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.weixiuren=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and s.offtime BETWEEN #{startDate}
                            AND #{endDate}

                          UNION all

                          SELECT 2 type,
                              s.id,w.id wxkcoutputId,
                              0.0 维修配件费用,
                              0.0 手工费用,
                              0.0 回收抵扣金额,
                              -isnull(case when w.ppriceid > 0 and isnull(w.tui_status, 0) != 1 then w.inprice else 0.0 end,
                              0.0) 维修配件成本,
                              -isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
                              w.tuidtime 办理时间,
                              0 维修配件优惠费用,
                              0 手工优惠费用
                          FROM dbo.shouhou s
                          with (nolock)
                              left join dbo.productinfo p (nolock)
                          on p.ppriceid=s.ppriceid
                              inner join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id and w.stats = 3
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.weixiuren=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and w.tuidtime BETWEEN #{startDate}
                            AND #{endDate}

                          UNION all

                          SELECT 3 type,
                              s.id,w.id wxkcoutputId,
                              -isnull(case
                              when w.ppriceid > 0 and w.service_type > 0 then w.price1
                              when w.ppriceid > 0 then w.price
                              else 0.0 end, 0.0) 维修配件费用,
                              -isnull(case when w.ppriceid > 0 then 0.0 when w.service_type > 0 then w.price1 else w.price end,
                              0.0) 手工费用,
                              -isnull(cast (hs.price AS DECIMAL (16, 2)), 0.00) 回收抵扣金额,
                              0 维修配件成本,
                              0 手工费用成本,
                              st.check3dtime 办理时间,
                              -isnull(case
                              when w.ppriceid > 0 then
                              (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                              + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00))
                              else 0 end, 0.0) 维修配件优惠费用,
                              -isnull(case
                              when w.ppriceid > 0 then 0
                              else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
                              + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00)) end,
                              0.0) 手工优惠费用
                          FROM dbo.shouhou s
                          with (nolock)
                              inner join (select st.id,
                              st.shouhou_id,
                              st.dtime,
                              st.check3dtime,
                              lag(st.dtime, 1) over (PARTITION by st.shouhou_id order by st.dtime asc) preStTime
                              from dbo.shouhou_tuihuan st with (nolock)
                              where isnull(st.isdel, 0) = 0
                              and st.check3dtime BETWEEN #{startDate} AND #{endDate}
                              and st.tuihuan_kind in (5, 11)
                              and st.check3 = 1) st
                          on st.shouhou_id = s.id and st.check3dtime > s.offtime
                              left join dbo.wxkcoutput w
                          with (nolock)
                          on w.wxid = s.id and w.stats = 3 and (w.fk_tuihuan_id = st.id or w.tuidtime >= isnull(st.preStTime,s.offtime) and (w.tuidtime &lt; st.dtime or ABS(DATEDIFF(minute, cast(w.tuidtime as datetime),st.dtime)) &lt;= 1))
                              left join dbo.shouhou_huishou hs
                          with (nolock)
                          on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
                              LEFT JOIN dbo.ch999_user cc
                          WITH (NOLOCK)
                          ON s.weixiuren=cc.ch999_name
                          where s.isquji = 1
                            and cc.ch999_id=#{ch999Id}
                            and w.id is not null
                      ) s
             ) result group by subId,userType,wxkcoutputId
    </select>
    <select id="getAfterOrderTrueProfitVoOfArea" resultType="com.jiuji.oa.salary.AfterOrderTrueProfitVo">
        select subId,wxkcoutputId,areaId,sum(trueProfitTemp) trueProfit
        from (
        SELECT s.id as subId,s.areaId,s.wxkcoutputId,
        (s.维修配件费用 - s.维修配件成本 - s.回收抵扣金额 - s.维修配件优惠费用) +
        (s.手工费用 - s.手工费用成本 - s.手工优惠费用) trueProfitTemp
        from (
        SELECT
        s.id,w.id wxkcoutputId,
        s.areaId,
        isnull(case
        when w.ppriceid > 0 and w.service_type > 0 then w.price1
        when w.ppriceid > 0 then w.price
        else 0.0 end, 0.0) 维修配件费用,
        isnull((case
        when w.ppriceid > 0 then 0.0
        when w.service_type > 0 then w.price1
        else w.price end),
        0.0) 手工费用,
        isnull(cast(hs.price AS DECIMAL(16, 2)), 0.0) 回收抵扣金额,
        isnull(case when w.ppriceid > 0 then w.inprice else 0.0 end, 0.0) 维修配件成本,
        isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
        s.offtime 办理时间,
        isnull(case
        when w.ppriceid > 0 then
        (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
        +
        isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
        0.0) 维修配件优惠费用,
        isnull(case
        when w.ppriceid > 0 then 0
        else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
        +
        isnull(cast(w.price - w.wxtongjitotal AS DECIMAL(16, 2)), 0.00) end,
        0.0) 手工优惠费用
        FROM dbo.shouhou s with (nolock)
        inner join dbo.wxkcoutput w
        with (nolock)
        on w.wxid = s.id
        left join dbo.shouhou_huishou hs
        with (nolock)
        on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
        where s.isquji = 1
        AND s.areaid IN
        <foreach collection="req.areaIdList" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        and s.offtime BETWEEN #{req.startDate} AND #{req.endDate}

        UNION all

        SELECT
        s.id,w.id wxkcoutputId,
        s.areaId,
        0.0 维修配件费用,
        0.0 手工费用,
        0.0 回收抵扣金额,
        -isnull(case when w.ppriceid > 0 and isnull(w.tui_status, 0) != 1 then w.inprice else 0.0 end,
        0.0) 维修配件成本,
        -isnull(case when w.ppriceid > 0 then 0.0 else w.inprice end, 0.0) 手工费用成本,
        w.tuidtime 办理时间,
        0 维修配件优惠费用,
        0 手工优惠费用
        FROM dbo.shouhou s
        with (nolock)
        left join dbo.productinfo p (nolock)
        on p.ppriceid=s.ppriceid
        inner join dbo.wxkcoutput w
        with (nolock)
        on w.wxid = s.id and w.stats = 3
        where s.isquji = 1
        AND s.areaid IN
        <foreach collection="req.areaIdList" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        and w.tuidtime BETWEEN #{req.startDate} AND #{req.endDate}

        UNION all

        SELECT
        s.id,w.id wxkcoutputId,
        s.areaId,
        -isnull(case
        when w.ppriceid > 0 and w.service_type > 0 then w.price1
        when w.ppriceid > 0 then w.price
        else 0.0 end, 0.0) 维修配件费用,
        -isnull(case when w.ppriceid > 0 then 0.0 when w.service_type > 0 then w.price1 else w.price end,
        0.0) 手工费用,
        -isnull(cast (hs.price AS DECIMAL (16, 2)), 0.00) 回收抵扣金额,
        0 维修配件成本,
        0 手工费用成本,
        st.check3dtime 办理时间,
        -isnull(case
        when w.ppriceid > 0 then
        (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
        + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00))
        else 0 end, 0.0) 维修配件优惠费用,
        -isnull(case
        when w.ppriceid > 0 then 0
        else (case when w.service_type > 0 then w.price1 - w.price else 0.0 end)
        + (isnull(cast (w.price - w.wxtongjitotal AS DECIMAL (16, 2)), 0.00)) end,
        0.0) 手工优惠费用
        FROM dbo.shouhou s
        with (nolock)
        inner join (select st.id,
        st.shouhou_id,
        st.dtime,
        st.check3dtime,
        lag(st.dtime, 1) over (PARTITION by st.shouhou_id order by st.dtime asc) preStTime
        from dbo.shouhou_tuihuan st with (nolock)
        where isnull(st.isdel, 0) = 0
        and st.check3dtime BETWEEN #{req.startDate} AND #{req.endDate}
        and st.tuihuan_kind in (5, 11)
        and st.check3 = 1) st
        on st.shouhou_id = s.id and st.check3dtime > s.offtime
        left join dbo.wxkcoutput w
        with (nolock)
        on w.wxid = s.id and w.stats = 3 and (w.fk_tuihuan_id = st.id or w.tuidtime >= isnull(st.preStTime,s.offtime) and (w.tuidtime &lt; st.dtime or ABS(DATEDIFF(minute, cast(w.tuidtime as datetime),st.dtime)) &lt;= 1))
        left join dbo.shouhou_huishou hs
        with (nolock)
        on hs.wxkcid = w.id and hs.isdel = 0 and hs.price>0
        where s.isquji = 1
        AND s.areaid IN
        <foreach collection="req.areaIdList" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        and w.id is not null
        ) s
        ) result group by subId,areaId,wxkcoutputId
    </select>

    <select id="getShouYinSubPay" resultType="com.jiuji.oa.oacore.salary.bo.dto.ShouYinSubPayDto">
        SELECT sy.sub_id,
        case when isnull(sy.sub_pay01,0) > 0 then '100001' end sub_pay01,
        case when isnull(sy.sub_pay02,0) > 0 then '100002'  end sub_pay02,
        case when isnull(sy.sub_pay03,0) > 0 then '100003' end sub_pay03,
        case when isnull(sy.sub_pay06,0) > 0 then '100006'  end sub_pay06,
        case when isnull(sy.sub_pay07,0) > 0 then '100007'  end sub_pay07,
        case when isnull(sy.sub_pay08,0) > 0 then '100008' end sub_pay08
        FROM dbo.shouying sy WITH (NOLOCK)
        where sy.sub_id in
        <foreach collection="subIds" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listRecoverCheckUserDiffRate" resultType="com.jiuji.oa.salary.RecoverCheckUserDiffRate">
        SELECT u.ch999_id,
               CASE
                   WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收量, 2) AS DECIMAL(18, 2))
                   ELSE 0 END returnDifferenceRateUser,
               CASE
                   WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收总量, 2) AS DECIMAL(18, 2))
                   ELSE 0 END returnTotalDifferenceRateUser
        FROM (SELECT b.ch999_name,
                     SUM(CASE WHEN b.kinds = 5 THEN b.val ELSE 0 END)  回收总量,
                     SUM(CASE WHEN b.kinds = 5 THEN b.val2 ELSE 0 END) 回收差异量,
                     SUM(CASE WHEN b.kinds = 5 THEN b.val3 ELSE 0 END) 回收量
              FROM (
                       SELECT b.checkUser                                                  ch999_name,
                              s.areaid,
                              COUNT(1)                                                     val,
                              SUM(CASE WHEN ISNULL(k.diffopt, '') &lt;&gt; '' THEN 1 ELSE 0 END) val2,
                              SUM(CASE WHEN ISNULL(b.price, 0) >= 21 THEN 1 ELSE 0 END)    val3,
                              5                                                            kinds
                       FROM dbo.recover_sub s with (nolock)
        LEFT JOIN dbo.recover_basket b with (nolock)
                       ON s.sub_id = b.sub_id
                           LEFT JOIN dbo.recover_mkc k with (nolock)
                       ON k.from_basket_id = b.id AND ISNULL(k.ishouhou, 0) = 0
                           LEFT JOIN dbo.recover_marketSubInfo rm with (nolock)
                       ON k.to_basket_id = rm.basket_id
                           LEFT JOIN dbo.recover_marketInfo rs with (nolock)
                       ON rm.sub_id = rs.sub_id
                       WHERE s.sub_check in (3)
                         AND ISNULL(b.isdel, 0) = 0
                         and rs.tradeDate1 between #{startDate} and #{endDate}
                         and isnull(rs.sub_to, '') &lt;&gt; '回收机退回渠道'
                       GROUP BY b.checkUser, s.areaid
                       UNION ALL
                       SELECT b.checkUser ch999_name,
                           s.areaid,
                           NULL        val,
                           NULL        val2,
                           NULL        val3,
                           5           kinds
                       FROM dbo.recover_sub s with (nolock)
                           LEFT JOIN dbo.recover_basket b with (nolock)
                       ON s.sub_id = b.sub_id
                           LEFT JOIN dbo.recover_mkc k with (nolock)
                       ON k.from_basket_id = b.id AND ISNULL(k.ishouhou, 0) = 0
                           LEFT JOIN dbo.recover_marketSubInfo rm with (nolock)
                       ON k.to_basket_id = rm.basket_id
                           LEFT JOIN dbo.recover_marketInfo rs with (nolock)
                       ON rm.sub_id = rs.sub_id
                       WHERE s.sub_check in (3)
                         AND ISNULL(b.isdel, 0) = 0
                         and s.pay_time between #{startDate} and #{endDate}
                         AND isnull(rs.sub_to, '') &lt;&gt; '回收机退回渠道'
                       GROUP BY b.checkUser, s.areaid
                   ) b
              GROUP BY b.ch999_name) t1
                 INNER JOIN dbo.ch999_user u WITH (NOLOCK)
        ON u.ch999_name = t1.ch999_name
            LEFT JOIN dbo.SalesTask t WITH (NOLOCK)
        ON t.type_ = 3 AND t.TaskDate = CONVERT(varchar(6), #{startDate}, 112) AND
            t.fieldVal = CAST(u.ch999_id AS VARCHAR(20))
    </select>
    <select id="getNationalSubCouponDto" resultType="com.jiuji.oa.oacore.salary.bo.dto.NationalSubCouponDto">
        select s.sub_id,
               case when r.businessType = 1 then 0 - r.youhui_price else r.youhui_price end youhuiPrice
        from dbo.sub_basket_coupon_record r with (nolock)
         inner join dbo.basket b WITH (NOLOCK) ON r.link_basket_id = b.basket_id
            INNER join dbo.sub s WITH (NOLOCK) ON b.sub_id = s.sub_id

        WHERE ISNULL(b.isdel, 0) = 0
          and ISNULL(r.is_del, 0) = 0
          AND s.sub_check in (3, 9)
          and ISNULL(b.ismobile,0)=1
          AND r.create_time BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>
