<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.weborder.dao.WebOrderMapper">
    <sql id="normalQuery">
        WHERE 1=1 and s.userid = #{userId}
        <if test = "req.displayInvoice != null and req.displayInvoice == true">
            and datediff(day,s.tradeDate1,getdate())&lt;120
            and NOT EXISTS(select 1 from tax_piao t with(nolock ) where isnull(flag,0) in (0, 1, 2, 3, 4) AND t.type_ = 0
            and ( t.sub_id = s.sub_id or exists (select 1 from dbo.piaoProductInfo p with(nolock) where t.id = p.piaoid and p.sub_id = s.sub_id )))

        </if>
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id)
        </if>
        <if test="searchId != null and searchId !=0">
            AND EXISTS(
            SELECT 1 FROM dbo.basket b WITH (NOLOCK)
            left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
            WHERE s.sub_id = b.sub_id and (b.sub_id = #{searchId} or b.ppriceid = #{searchId} or p.productid =
            #{searchId}
            <if test="keyWord != null and keyWord.length() &lt; 5">
                or p.product_name like CONCAT('%',#{keyWord},'%')
            </if>
            ))
        </if>
        <if test="status != null">
            and s.sub_check in
            <foreach collection="status" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="keyWord == null or keyWord ==''">
            AND EXISTS(select 1 from dbo.basket b with (nolock) where s.sub_id = b.sub_id)
        </if>
        <if test="(searchId==null or searchId==0) and keyWord != null and keyWord !=''">
            AND EXISTS(SELECT 1 FROM dbo.basket b WITH (NOLOCK)
            left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
            WHERE s.sub_id = b.sub_id and p.product_name like CONCAT('%',#{keyWord},'%'))
        </if>
        <choose>
            <when test="tag == null || tag=='' || tag =='1_1'">
            </when>
            <when test="tag == '1_2' ">
                and isnull(s.islock,0) = 0
                and s.yingfuM !=0 and s.yifuM &lt; s.yingfuM and s.sub_check in(0,1,2,5,6,7)
                <if test="orderLinkFlag == null or orderLinkFlag != 1">
                and not (s.delivery = 1 and s.sub_pay = 10)
                </if>
            </when>
            <when test="tag == '1_3'">
                and (s.sub_check = 2
                or (s.yingfuM = s.yifuM and s.sub_check in (0,1,2,5,6,7))
                or (s.yingfuM != s.yifuM and s.sub_check in (0,1,2,5,6,7) and s.delivery=1 and s.sub_pay=10))
            </when>
            <when test="tag == '1_4'">
                and s.sub_check = 3
            </when>
            <when test="tag == '1_5'">
                and s.sub_check in (4,8,9)
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test="req.delCollect != null and req.delCollect == true">
                and exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=1 and c.del_type = 0)
            </when>
            <otherwise>
                and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=1)
            </otherwise>
        </choose>
        <if test="distribution != null and distribution == true">
            and s.subtype = 10
        </if>
        AND (s.sub_check &lt;&gt; 4 OR s.sub_date &gt;= DATEADD(DAY,-30,getdate()))
        <if test="hideOrder != null and hideOrder == 1 ">
            and not exists(select 1 from subFlagRecord sfr with(nolock) where sfr.flagType = 4 and sfr.status = 1 and sfr.sub_id = s.sub_id)
        </if>
        <if test="req != null">
            <if test="req.subIds != null and !req.subIds.isEmpty()">
                and s.sub_id in
                <foreach collection="req.subIds" item="subId" open="(" close=")" separator=",">
                    #{subId}
                </foreach>
            </if>
        </if>
        <if test="req.excludeNotShouYingBasketTypes !=null and req.excludeNotShouYingBasketTypes.size() != 0 ">
            and (not EXISTS(select 1 from dbo.basket b with(nolock ) where b.sub_id = s.sub_id
            and b.type in
            <foreach collection="req.excludeNotShouYingBasketTypes" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
                and not EXISTS(SELECT * from shouying sy with(nolock) where sy.sub_id = s.sub_id and sy.shouying_type in ('订金', '交易'))
                )
            )
        </if>
    </sql>

    <sql id="normalSelect">
        SELECT null           as recoverSubId, <!--回收单单独获取, 存在多个情况-->
               s.sub_date          as addTime,
               s.tradeDate1        as finishTime,
               s.sub_check         as subCheck,
               s.sub_id            as subId,
               s.subtype           as subType,
               s.userid            as userId,
               isnull(s.yingfuM,0)+isnull(s.coinM,0) as yingfuM,
               isnull(s.yifuM,0) + isnull(s.coinM,0) as yifuM,
               s.delivery          as delivery,
               s.areaid            as areaId,
               s.kcAreaid          as kcAreaId,
               s.trader            AS sender,
               s.expectTime        as expectTime,
               addr.paisongState   as paisongState,
               addr.Address        as subAddress,
               addr.cityid         as cityId,
               s.sub_pay           as subPay,
               isnull(s.islock, 0) as isLock,
               s.zitidianID        as zitidianId,
               addr.expectTime     as autoDelDate,
               s.jidianM
    </sql>

    <select id="countNormal" resultType="java.lang.Integer">
        select count(s.sub_id) from sub s with(nolock)
        <!--left join recover_sub ss with(nolock) on ss.sub_ido = s.sub_id AND ISNULL(ss.subIdoType,1) = 1 -->
        <include refid="normalQuery">
        </include>
    </select>

    <select id="countNormalHis" resultType="java.lang.Integer">
        select count(s.sub_id) from sub s with(nolock)
        <!--left join recover_sub ss with(nolock) on ss.sub_ido = s.sub_id AND ISNULL(ss.subIdoType,1) = 1 -->
        <include refid="normalQuery">
        </include>
    </select>

    <select id="listNormal" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiSubBO">
        <include refid="normalSelect">
        </include>
        from sub s with(nolock)
        left join dbo.SubAddress addr with(nolock) on addr.sub_id = s.sub_id
        <!--left join recover_sub ss with(nolock) on ss.sub_ido = s.sub_id AND ISNULL(ss.subIdoType,1) = 1 -->
        <include refid="normalQuery">
        </include>
        order by s.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <select id="listNormalHis" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiSubBO">
        <include refid="normalSelect">
        </include>
        from sub s with(nolock)
        left join dbo.SubAddress addr with(nolock) on addr.sub_id = s.sub_id
        <!--left join recover_sub ss with(nolock) on ss.sub_ido = s.sub_id AND ISNULL(ss.subIdoType,1) = 1 -->
        <include refid="normalQuery">
        </include>
        order by s.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <select id="listNormalStatusBySubId" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiSubBO">
        SELECT
        s.sub_date as addTime,
        s.tradeDate1 as finishTime,
        s.sub_check as subCheck,
        s.sub_id as subId,
        s.yingfuM as yingfuM,
        s.yifuM as yifuM,
        isnull(s.islock,0) as isLock
        from sub s with(nolock)
        where s.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="listNormalStatusBySubIdHis" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiSubBO">
        SELECT
        s.sub_date as addTime,
        s.tradeDate1 as finishTime,
        s.sub_check as subCheck,
        s.sub_id as subId,
        s.yingfuM as yingfuM,
        s.yifuM as yifuM,
        isnull(s.islock,0) as isLock
        from sub s with(nolock)
        where s.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="listDelCollectBySubIdAndType" resultType="java.lang.Integer">
        select c.sub_id
        from dbo.sub_delCollect c with(nolock) where c.subType=#{type}
        and c.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <sql id="normalProductSelect">
        select b.sub_id       as                                          subId,
               b.ismobile     as                                          isMobile,
               b.price        as                                          price,
               b.price1       as                                          originalPrice,
               b.type         as                                          type,
               b.basket_id    as                                          basketId,
               b.ppriceid     as                                          ppid,
               b.basket_id    as                                          basketId,
               b.giftid       as                                          giftId,
               b.basket_count as                                          productCount,
               case when b.ismobile = 1 then k.mkcCount else o.lcount end beiCount,
               s.delivery     as                                          delivery,
               mk.imei        as                                          imei
        from dbo.basket b with (nolock)
                     left join dbo.sub s
                with (nolock)
                on b.sub_id = s.sub_id
                     left join dbo.basket_other o
                with (nolock)
                on o.basket_id = b.basket_id
                     LEFT JOIN dbo.product_mkc mk
                with (nolock)
                ON mk.basket_id = b.basket_id
                     left join (select basket_id, count(1) mkcCount
                                from dbo.product_mkc with (nolock)
                                where kc_check in (3)
                                group by basket_id) k
                on k.basket_id = b.basket_id
    </sql>

    <select id="listProductNormal" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiProductBO">
        <include refid="normalProductSelect">
        </include>
        where ISNULL(b.isdel,0) = 0 and b.sub_id in
        <foreach collection="subIds" open="(" separator="," close=")" item="subId">
            #{subId}
        </foreach>
        UNION ALL
        <include refid="normalProductSelect">
        </include>
        where s.sub_check = 4
        and b.sub_id in
        <foreach collection="subIds" open="(" separator="," close=")" item="subId">
            #{subId}
        </foreach>
    </select>

    <select id="listProductNormalHis" resultType="com.jiuji.oa.oacore.weborder.bo.XinjiProductBO">
        <include refid="normalProductSelect">
        </include>
        where ISNULL(b.isdel,0) = 0 and b.sub_id in
        <foreach collection="subIds" open="(" separator="," close=")" item="subId">
            #{subId}
        </foreach>
        UNION ALL
        <include refid="normalProductSelect">
        </include>
        where s.sub_check = 4
        and b.sub_id in
        <foreach collection="subIds" open="(" separator="," close=")" item="subId">
            #{subId}
        </foreach>
    </select>

    <select id="checkEvaluatedInType" resultType="java.lang.Integer">
        select e.subid
        from ${officeName}.dbo.evaluate e with(nolock)
        where e.EvaluateType in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type.code}
        </foreach>
        and e.subid in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="checkEvaluatedByType" resultType="java.lang.Integer">
        select e.subid
        from ${officeName}.dbo.evaluate e with(nolock)
        where e.EvaluateType = #{type.code}
        and e.subid in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>


    <select id="listHuishouByRecoverSubId" resultType="com.jiuji.oa.oacore.weborder.bo.HuiShouSubBO">
        SELECT
        s.sub_id as recoverSubId,
        s.sub_ido as subId,
        s.sub_check as subCheck,
        s.sub_address as subAddress,
        s.sub_delivery as delivery,
        s.areaid as areaId,
        s.dtime as addTime,
        s.pay_time as finishTime
        FROM dbo.recover_sub s with (nolock)
        where
        <choose>
            <when test="allSubIds != null and !allSubIds.isEmpty()">
                s.sub_ido in
                <foreach collection="allSubIds" item="subId" open="(" separator="," close=")">
                    #{subId}
                </foreach>
                AND ISNULL(s.subIdoType,1) = #{subIdoType}
            </when>
            <otherwise>
                s.sub_id in
                <foreach collection="recoverSubIds" item="subId" open="(" separator="," close=")">
                    #{subId}
                </foreach>
            </otherwise>
        </choose>


        and s.sub_check != 4
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=6)
    </select>

    <sql id="huishouProductSelect">
        SELECT b.ppriceid as ppid,
               b.price    as price,
               b.id       as basketId,
               b.sub_id      recoverSubId,
               b.minPrice as minPrice,
               b.maxPrice as maxPrice,
               s.kinds    as kinds,
               s.sub_tel as mobile,
               p.status as assistStatus,
               ISNULL(k.ishouhou,0) ishouhou,
               ph.product_name as productName,
               ph.product_color as skuDesc
        FROM dbo.recover_basket b with (nolock)
        INNER JOIN dbo.recover_sub s with (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.recover_mkc k with(nolock,index=index_frombasketid) ON k.from_basket_id = b.id AND ISNULL(k.ishouhou,0) &lt;&gt; 1
		LEFT JOIN dbo.recover_helpSell_product p with(nolock) ON p.mkc_id = k.id
        LEFT JOIN dbo.productinfo_huishou ph with(nolock) on b.ppriceid = ph.ppriceid
    </sql>

    <select id="listHuishouProductByRecoverSubId" resultType="com.jiuji.oa.oacore.weborder.bo.HuiShouProductBO">
        <include refid="huishouProductSelect">
        </include>
        WHERE ISNULL(b.isdel, 0) = 0
        AND s.sub_check &lt;> 4
        AND s.sub_id in
        <foreach collection="recoverSubIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        UNION ALL
        <include refid="huishouProductSelect">
        </include>
        WHERE s.sub_check = 4
        AND s.sub_id in
        <foreach collection="recoverSubIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>


    <select id="listCh999UserServiceBO" resultType="com.jiuji.oa.oacore.weborder.bo.Ch999UserServiceBO">
        select
        e.Id as id,
        e.RelateCh999Id as ch999Id,
        e.Job as job,
        e.uPrices as uPrices,
        u.area1id as area1id,
        e.sub_id subId
        from ${officeName}..EvaluateScore e with (nolock)
        left join dbo.ch999_user u with (nolock) on e.RelateCh999Id = u.ch999_id
        where e.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        and e.type_ in (1, 2, 6)
        and e.RelateCh999Id &lt;> 0
    </select>

    <sql id="liangpinSelect">
        SELECT distinct
               null            as recoverSubId,
               rm.sub_date          as addTime,
               rm.tradeDate1        as finishTime,
               rm.sub_check         as subCheck,
               rm.sub_id            as subId,
               rm.userid            as userId,
               rm.yingfuM           as yingfuM,
               rm.yifuM             as yifuM,
               rm.delivery          as delivery,
               rm.areaid            as areaId,
               w.paijianren         AS sender,
               rm.expectTime        as expectTime,
               addr.paisongState    as paisongState,
               addr.Address         as subAddress,
               addr.cityid          as cityId,
               rm.sub_pay           as subPay,
               isnull(rm.islock, 0) as isLock,
               rm.zitidianID        as zitidianId,
               rm.jidianM
    </sql>

    <sql id="liangpinQuery">
        from recover_marketInfo rm with(nolock)
        LEFT JOIN dbo.wuliu w with (nolock) ON (rm.sub_id = w.danhaobind and w.wutype = 9)
        left join dbo.RecoverSubAddress addr with (nolock) on addr.sub_id = rm.sub_id
        WHERE 1=1 and rm.userid = #{userId} AND ISNULL(rm.saleType,0)=0
        <if test = "displayInvoice != null and displayInvoice == true">
            <if test="xtenant != null and xtenant &lt; 1000">
                and datediff(day,rm.tradeDate1,getdate()) &gt; 7
            </if>
            and datediff(day,rm.tradeDate1,getdate())&lt;120
            and NOT EXISTS(select 1 from tax_piao t with(nolock ) where isnull(flag,0) in (0, 1, 2, 3, 4) AND t.type_ = 2
            and ( t.sub_id = rm.sub_id or exists (select 1 from dbo.piaoProductInfo p with(nolock) where t.id = p.piaoid and p.sub_id = rm.sub_id )))

        </if>
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where rm.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where rm.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2 and (xtenant&lt;2000 or xtenant&gt;5000) ">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where rm.areaid=ar.id AND ar.xtenant = #{xtenant})
        </if>
        <if test="status != null">
            and rm.sub_check in
            <foreach collection="status" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <choose>
            <when test="tag == null || tag=='' || tag =='1_1'">
            </when>
            <when test="tag == '2_2' ">
                and rm.yingfuM !=0 and rm.yifuM &lt; rm.yingfuM and rm.sub_check in(0,1,2,5,7)
                <if test="req.orderLinkLingPingFlag != null or req.orderLinkLingPingFlag == 1">
                    and not (rm.delivery = 1 and rm.sub_pay = 10)
                </if>
            </when>
            <when test="tag == '2_3'">
                and (rm.sub_check = 2
                or (rm.yingfuM !=0 and rm.yingfuM = rm.yifuM and rm.sub_check in (0,1,2,5,7))
                or (rm.yingfuM != rm.yifuM and rm.sub_check in (0,1,2,5,7) and rm.delivery=1 and rm.sub_pay=10))
            </when>
            <when test="tag == '2_4'">
                and rm.sub_check = 3
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="searchId != null and searchId !=0">
            and exists(select 1 FROM dbo.recover_marketSubInfo rms with (nolock)
            LEFT JOIN recover_mkc k with (nolock) ON rms.basket_id = k.to_basket_id
            LEFT JOIN dbo.productinfo p with (nolock) ON p.ppriceid = k.ppriceid
            LEFT JOIN dbo.productinfo p2 with (nolock) on p2.ppriceid = rms.ppriceid
            WHERE rm.sub_id = rms.sub_id and rms.giftid is NULL
            and (rms.sub_id=#{searchId}
            or k.ppriceid=#{searchId} or rms.ppriceid=#{searchId}
            or p.productid=#{searchId} or p2.productid=#{searchId})
            <if test="keyWord != null and keyWord.length() &lt; 5">
                or (p.product_name like CONCAT('%',#{keyWord},'%') or p2.product_name like CONCAT('%',#{keyWord},'%'))
            </if>
            )
        </if>
        <if test="(searchId==null or searchId==0) and keyWord != null and keyWord !=''">
            and exists(select 1 FROM dbo.recover_marketSubInfo rms with (nolock)
            LEFT JOIN recover_mkc k with (nolock) ON rms.basket_id = k.to_basket_id
            LEFT JOIN dbo.productinfo p with (nolock) ON p.ppriceid = k.ppriceid
            LEFT JOIN dbo.productinfo p2 with (nolock) on p2.ppriceid = rms.ppriceid
            WHERE rm.sub_id = rms.sub_id and rms.giftid is NULL
            and (p.product_name like CONCAT('%',#{keyWord},'%') or p2.product_name like CONCAT('%',#{keyWord},'%')))
        </if>
        <choose>
            <when test="req.delCollect != null and req.delCollect == true">
                and exists (select 1 from dbo.sub_delCollect c with(nolock) where rm.sub_id = c.sub_id and c.subType=2 and c.del_type = 0)
            </when>
            <otherwise>
                and not exists (select 1 from dbo.sub_delCollect c with(nolock) where rm.sub_id = c.sub_id and c.subType=2)
            </otherwise>
        </choose>
        AND (rm.sub_check &lt;> 4 OR DATEDIFF(DAY,rm.sub_date,GETDATE()) &lt;=30)
    </sql>

    <select id="countLiangpin" resultType="java.lang.Integer">
        select count(1)
        <include refid="liangpinQuery">
        </include>
    </select>

    <select id="listLiangpin" resultType="com.jiuji.oa.oacore.weborder.bo.LiangpinSubBO">
        <include refid="liangpinSelect">
        </include>
        <include refid="liangpinQuery">
        </include>
        order by rm.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <sql id="liangpinProductSelect">
        SELECT rms.sub_id   as subId,
               rms.ismobile as isMobile,
               rms.price    as price,
               k.imei       as imei,
               rms.ppriceid as ppid,
               k.ppriceid   as ppid2,
               k.mkc_check as mkcCheck,
               k.areaid as areaId,
               rms.mkc_id2  as mkcId,
               rm.delivery
        FROM dbo.recover_marketSubInfo rms with (nolock)
                     LEFT JOIN recover_mkc k
                with (nolock)
                ON rms.basket_id = k.to_basket_id
                     LEFT JOIN dbo.recover_marketInfo rm
                with (nolock)
                ON rm.sub_id = rms.sub_id
        WHERE rms.giftid is NULL
    </sql>

    <select id="listProductLiangpin" resultType="com.jiuji.oa.oacore.weborder.bo.LiangpinProductBO">
        <include refid="liangpinProductSelect">
        </include>
        and ISNULL(rms.isdel,0) = 0
        AND rms.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        UNION ALL
        <include refid="liangpinProductSelect">
        </include>
        and rm.sub_check = 4
        AND rms.sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <sql id="recoverSelect">
        select s.sub_id       as recoverSubId,
               s.sub_check    as subCheck,
               s.sub_delivery as delivery,
               s.sub_address     subAddress,
               s.areaid       as areaId,
               s.dtime        as addTime,
               s.pay_time     as finishTime,
               s.kuaidigongsi as kuaiDiGongsi,
               s.express_recover_status as expressRecoverStatus
    </sql>

    <sql id="recoverQuery">
        FROM dbo.recover_sub s with (nolock)
        where s.userid =#{userId}
        and s.sub_check NOT IN (4,9)
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2 and (xtenant&lt;2000 or xtenant&gt;5000)">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id AND ar.xtenant = #{xtenant})
        </if>
        <if test="keyWord == null or keyWord == ''">
            AND EXISTS(select 1 from dbo.recover_basket b with (nolock,index=index_subid) where s.sub_id = b.sub_id and
            isnull(b.isdel,0)=0)
        </if>
        <if test="status != null">
            and s.sub_check in
            <foreach collection="status" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="searchId != null and searchId != 0">
            AND EXISTS(
            select 1 from dbo.recover_basket b with (nolock)
            left join dbo.productinfo p with (nolock) on b.ppriceid=p.ppriceid
            where s.sub_id = b.sub_id and isnull(b.isdel,0)=0
            and (s.sub_id =#{searchId} or b.ppriceid=#{searchId} or p.productid=#{searchId}
            <if test="keyWord != null and keyWord.length() &lt; 5">
                or p.product_name like CONCAT('%',#{keyWord},'%')
            </if>
            )
            )
        </if>
        <if test="(searchId==null or searchId==0) and keyWord != null and keyWord != ''">
            AND EXISTS(select 1 from dbo.recover_basket b with (nolock)
            left join dbo.productinfo p with (nolock) on b.ppriceid=p.ppriceid
            where s.sub_id = b.sub_id and isnull(b.isdel,0)=0
            and p.product_name like CONCAT('%',#{keyWord},'%'))
        </if>
        <choose>
            <when test="tag == null || tag=='' || tag =='6_1'">
            </when>
            <when test="tag == '6_2' ">
                and s.sub_check in (0,1)
            </when>
            <when test="tag == '6_3'">
                and s.sub_check in (2,5)
            </when>
            <when test="tag == '6_4'">
                and s.sub_check = 3
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test="req.delCollect != null and req.delCollect == true">
                and exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=6 and c.del_type = 0)
            </when>
            <otherwise>
                and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=6)
            </otherwise>
        </choose>
    </sql>

    <select id="countRecover" resultType="java.lang.Integer">
        select count(1)
        <include refid="recoverQuery">
        </include>
    </select>

    <select id="listRecover" resultType="com.jiuji.oa.oacore.weborder.bo.HuiShouSubBO">
        <include refid="recoverSelect">
        </include>
        <include refid="recoverQuery">
        </include>
        order by s.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>


    <sql id="afterServiceQuery">
        FROM shouhou s with (nolock)
        LEFT JOIN dbo.shouhou_yuyue sy WITH (NOLOCK) ON s.yuyueid = sy.id
        left join ${officeName}.dbo.evaluate e with (nolock) on (e.subid = s.id and e.EvaluateType = 3)
        WHERE 1 = 1
        and ISNULL(s.xianshi, 0) = 1
        and isnull(s.issoft, 0) = 0
        AND s.userid = #{userId}
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id AND ar.xtenant = #{xtenant})
        </if>
        and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=0)
        <choose>
            <when test="req.delCollect != null and req.delCollect == true">
                and exists(select 1 from dbo.sub_delCollect c with (nolock) where s.id = c.sub_id and c.subType = 4 and c.del_type = 0)
            </when>
            <otherwise>
                and not exists(select 1 from dbo.sub_delCollect c with (nolock) where s.id = c.sub_id and c.subType = 4)
            </otherwise>
        </choose>
    </sql>

    <sql id="bigProQuery">
        FROM shouhou s with (nolock)
        LEFT JOIN dbo.shouhou_yuyue sy WITH (NOLOCK) ON s.yuyueid = sy.id
        left join ${officeName}.dbo.evaluate e with (nolock) on (e.subid = s.id and e.EvaluateType = 3)
        WHERE 1 = 1
        and ISNULL(s.xianshi, 0) = 1
        and isnull(s.issoft, 0) = 0
        AND s.userid = #{userId}
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where s.areaid=ar.id AND ar.xtenant = #{xtenant})
        </if>
        and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=0)
        and not exists(select 1 from dbo.sub_delCollect c with (nolock) where s.id = c.sub_id and c.subType = 4)
    </sql>

    <sql id="smallProQuery">
        FROM Smallpro s with (nolock)
        LEFT JOIN SmallproBill sb with (nolock) on s.id = sb.smallproID
        left join productinfo p  with (nolock) on p.ppriceid = sb.ppriceid
        left join ${officeName}.dbo.evaluate e with (nolock) on (e.subid = s.id and e.EvaluateType = 3)
        WHERE 1 = 1
        AND s.userid = #{userId}
        AND s.Stats != 2
        and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id)
    </sql>

    <select id="countAfterService" resultType="java.lang.Integer">
        select count(1)
        <include refid="afterServiceQuery">
        </include>
    </select>

    <select id="countSmallProAfterService" resultType="java.lang.Integer">
        select count(1)
        <include refid="smallProQuery">
        </include>
    </select>

    <select id="listAfterServiceSub" resultType="com.jiuji.oa.oacore.weborder.bo.AfterServiceSubBO">
        SELECT
        s.stats as state,
        s.id AS subId,
        s.modidate as addTime,
        s.offtime as finishTime,
        (case when e.id is null then 0 else 1 end) as ispj,
        isnull(isquji, 0) as isquji,
        s.shouyinglock as shouyingLock,
        sy.kind as kind,
        sy.stype as stype,
        s.areaid as areaId,
        s.problem as problem,
        sy.yuyuePPids as yuyuePpids,
        s.ppriceid as ppid,
        s.name AS productName,
        s.product_color AS productColor,
        s.feiyong as price,
        s.imei as imei
        <include refid="afterServiceQuery">
        </include>
        <if test="basketIdsBySubId != null and basketIdsBySubId.size() != 0">
            and s.basket_id in
            <foreach item="basketId" collection="basketIdsBySubId" separator="," open="(" close=")" index="">
                #{basketId}
            </foreach>
        </if>
        order by s.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <select id="listSmallProAfterServiceSub" resultType="com.jiuji.oa.oacore.weborder.bo.AfterServiceSubBO">
        SELECT
        s.stats as state,
        s.id AS subId,
        s.Indate as addTime,
        s.qujiandate as finishTime,
        1 as ispj,
        (case when s.qujiandate is null then 0 else 1 end) as isquji,
        s.isshouyinglock as shouyingLock,
        s.kind as kind,
        s.ServiceType as stype,
        s.areaid as areaId,
        s.problem as problem,
        '' as yuyuePpids,
        b.ppriceid as ppid,
        s.name AS productName,
        p.product_color AS productColor,
        s.feiyong as price,
        '' as imei
        <include refid="smallProQuery">
        </include>
        order by s.sub_id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <sql id="reservationQuery">
        FROM shouhou_yuyue sy with (nolock)
        LEFT JOIN shouhou sh WITH (nolock) ON sh.id = sy.shouhou_id
        WHERE 1 = 1
         <choose>
            <when test="req.isJiuJi != null and req.isJiuJi == 1">
                and sy.stats in (1,2,3,4,5,6,7,8,10)
            </when>
            <otherwise>
                and sy.stats IN (0, 1, 2, 4, 6, 7, 3)
            </otherwise>
        </choose>
        and sy.userid = #{userId}
        <if test="xtenant !=null and xtenant==0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where sy.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant !=null and xtenant==2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where sy.areaid=ar.id and ar.printName='华为授权' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant != null and xtenant!=0 and xtenant !=2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock)
            where sy.areaid=ar.id AND ar.xtenant = #{xtenant})
        </if>
        <choose>
            <when test="tag == null || tag=='' || tag =='6_1'">
            </when>
            <when test="tag == '3_1' ">
                <choose>
                    <when test="req.isJiuJi != null and req.isJiuJi == 1">
                        and sy.stats in (1,2,3,4,5,6,7,8,10)
                    </when>
                </choose>
            </when>
            <when test="tag == '3_2' ">
                 <choose>
                    <when test="req.isJiuJi != null and req.isJiuJi == 1">
                        and sy.stats in (1,2,4) and isnull(sy.isdel,0) = 0
                    </when>
                    <otherwise>
                        and sy.stats in (0,1,4,6,7) and isnull(sy.isdel,0) = 0
                    </otherwise>
                </choose>
            </when>
            <when test="tag == '3_3'">
                and sy.stats = 3 and isnull(sy.isdel,0) = 0
            </when>
            <when test="tag == '3_4'">
                <choose>
                    <when test="req.isJiuJi != null and req.isJiuJi == 1">
                        and (sy.isdel = 1 or sy.stats = 5)
                    </when>
                    <otherwise>
                        and sy.isdel = 1 AND sy.stats &lt;> 5
                    </otherwise>
                </choose>
             </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="(searchId==null or searchId==0) and keyWord != null and keyWord!=''">
            and sy.name like CONCAT('%',#{keyWord},'%')
        </if>
        <if test="searchId != null and searchId !=0">
            AND ((sy.id = #{searchId} or sh.id=#{searchId} or sy.ppriceid=#{searchId} or sh.ppriceid=#{searchId})
            or exists(select 1 from dbo.productinfo p with(nolock)
            where ((p.ppriceid=sh.ppriceid or p.ppriceid=sy.ppriceid)
            and p.productid=#{searchId}
                <if test="keyWord != null and keyWord.length() &lt; 5">
                    or sy.name like CONCAT('%',#{keyWord},'%')
                </if>
            )
            )
            )
        </if>
        <choose>
            <when test="req.delCollect != null and req.delCollect == true">
                and exists(select 1 from dbo.sub_delCollect c with (nolock) where sy.id = c.sub_id and c.subType = 3 and c.del_type = 0)
            </when>
            <otherwise>
                and not exists(select 1 from dbo.sub_delCollect c with (nolock) where sy.id = c.sub_id and c.subType = 3)
            </otherwise>
        </choose>
    </sql>

    <select id="countReservation" resultType="java.lang.Integer">
        select count(1)
        <include refid="reservationQuery">
        </include>
    </select>

    <select id="listReservationSub" resultType="com.jiuji.oa.oacore.weborder.bo.ReservationSubBO">
        select
        CASE WHEN sy.isdel = 1 and sy.stats &lt;> 5 THEN 10 ELSE sy.stats END as state,
        CASE WHEN sy.isdel = 1 or sy.stats = 5 THEN 5 ELSE sy.stats END as jiuState,
        sy.id AS subId,
        sy.dtime as addTime,
        sy.kind as kind,
        sy.stype as stype,
        sy.areaid as areaId,
        sy.ppriceid as syPpid,
        sy.shouhou_id as shId,
        sh.ppriceid as shPpid,
        sh.problem as problem,
        sy.yuyuePPids as yuyuePpids
        <include refid="reservationQuery">
        </include>
        order by sy.id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <select id="listExclusiveCustomerService" resultType="com.jiuji.oa.oacore.weborder.vo.Ch999UserServiceVO">
        select
        e.RelateCh999Id ch999_id,
        e.Job job,
        s.sub_id
        from
        ${officeName}..EvaluateScore e with(nolock)
        left join dbo.ch999_user u with(nolock) on
        e.RelateCh999Id = u.ch999_id
        inner join dbo.sub s with(nolock) on
        s.sub_id = e.sub_id
        where
        e.sub_id in
        <foreach collection="subIds" index="index" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        and e.type_ in (1, 2, 6)
        and e.RelateCh999Id != 0
        and u.iszaizhi = 1
    </select>

    <sql id="exclusiveCustomerOrderQuery">
        <!--15天内待评价订单-->
        SELECT s.sub_id FROM basket b with(nolock)
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN sub s with(nolock) ON b.sub_id = s.sub_id WHERE b.sub_id = (
        SELECT TOP 1 sub_id
        FROM ${officeName}.dbo.EvaluateScore es with(nolock)
        WHERE score IS null AND es.EvaluateId IS NULL
        AND type_ IN(1,2) AND userid = #{userId}
        AND DATEDIFF(DAY,es.dtime,GETDATE()) &lt;= 15
        AND not EXISTS(SELECT id FROM ${officeName}.dbo.Evaluate e with(nolock) WHERE e.SubId = es.sub_id)
        AND NOT EXISTS(select 1 from dbo.ch999_user with(nolock) where iszaizhi=1 and mobile in (select sub_mobile from
        dbo.sub with(nolock) where sub_id = es.sub_id))
        ORDER BY es.sub_id DESC
        )
        and s.sub_check = 3
        UNION ALL
        <!--订单-->
        SELECT s.sub_id
        FROM sub s with(nolock)
        left join dbo.SubAddress addr with(nolock) on addr.sub_id = s.sub_id
        left join ${officeName}.dbo.evaluate e with(nolock) on (e.subid = s.sub_id and e.EvaluateType in(1,2))
        left join recover_sub ss with(nolock) on ss.sub_ido = s.sub_id AND ISNULL(ss.subIdoType,1) = 1
        WHERE 1=1 and s.userid = #{userId} AND (s.sub_check != 4 OR DATEDIFF(DAY,s.sub_date,GETDATE()) &lt;= 30)
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=1 )
        AND DATEDIFF(MONTH,s.sub_date,GETDATE()) &lt;= 24
        <choose>
            <when test="xtenant != null and xtenant == 0">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='九机网' AND
                ar.xtenant = 0)
            </when>
            <when test="xtenant != null and xtenant == 2">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='华为授权')
            </when>
            <otherwise>
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=#{xtenant})
            </otherwise>
        </choose>
        UNION ALL
        <!--回收单-->
        SELECT sub_id
        FROM dbo.recover_sub s with(nolock)
        LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid=a.id
        WHERE s.userid = #{userId} and s.sub_check NOT IN(4,9)
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=6 )
        AND DATEDIFF(MONTH,s.dtime,GETDATE()) &lt;= 24
        <if test="xtenant != null and xtenant == 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='九机网' AND
            ar.xtenant = 0)
        </if>
        <if test="xtenant != null and xtenant == 2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='华为授权')
        </if>
    </sql>

    <select id="countExclusiveCustomerOrder" resultType="java.lang.Integer">
        select count(1) from (<include refid="exclusiveCustomerOrderQuery"/>) t
    </select>

    <select id="listExclusiveCustomerOrder" resultType="java.lang.Integer">
        select * from (<include refid="exclusiveCustomerOrderQuery"/>) t order by t.sub_id desc offset #{current} rows
        fetch next #{size} rows only
    </select>

    <select id="countNotEvaluatedOrder" resultType="int">
        select count(*)
        from sub s with (nolock)
        where s.userid = #{userId}
          and s.sub_check = 3
          and NOT EXISTS(select 1
                         from ${officeName}.dbo.Evaluate e with (nolock)
                         where s.sub_id = e.SubId
                           and e.EvaluateType in (1, 2))
          and NOT EXISTS(select 1
                         from dbo.sub_delCollect c with (nolock)
                         where s.sub_id = c.sub_id
                           and c.subType = 1)
    </select>
    <select id="getSaleOrders" resultType="com.jiuji.oa.oacore.weborder.vo.SaleOrderVO">
        SELECT basket_id,basket.sub_id,basket.ppriceid ppid,userid,sub.areaId,usr.userclass FROM dbo.basket with(nolock)
        INNER JOIN dbo.sub with(nolock) ON sub.sub_id = basket.sub_id
        INNER JOIN dbo.productinfo pro with(nolock) ON pro.ppriceid = basket.ppriceid
        LEFT JOIN dbo.BBSXP_Users usr with(nolock) ON usr.id = sub.userid
        WHERE ISNULL(basket.isdel,0) = 0 AND sub.yifuM >= #{pay}
        AND pro.productid in
        <foreach collection="productIds" index="index" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        AND ISNULL(usr.specialType,0) <![CDATA[&8]]> = 0
        AND sub.sub_check = 1 AND sub.islock is null
        ORDER BY CASE when usr.userclass=5 OR usr.userclass=6 THEN 1 ELSE 2 end ,basket_id
    </select>

    <select id="getSaleOrdersV2" resultType="com.jiuji.oa.oacore.weborder.vo.SaleOrderVO">
        SELECT basket_id,basket.sub_id,basket.ppriceid ppid,userid,sub.areaId,usr.userclass FROM dbo.basket with(nolock)
        INNER JOIN dbo.sub with(nolock) ON sub.sub_id = basket.sub_id
        INNER JOIN dbo.productinfo pro with(nolock) ON pro.ppriceid = basket.ppriceid
        LEFT JOIN dbo.BBSXP_Users usr with(nolock) ON usr.id = sub.userid
        WHERE ISNULL(basket.isdel,0) = 0 AND ISNULL(sub.yifuM, 0) + ISNULL(sub.coinM, 0) >= #{pay}
        AND pro.productid in
        <foreach collection="productIds" index="index" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        AND ISNULL(usr.specialType,0) <![CDATA[&8]]> = 0
        AND sub.sub_check = 1
        AND ISNULL(sub.islock, 0) = 0
        <if test="subDate != null and subDate != ''">
            and sub.sub_date <![CDATA[<=]]> #{subDate}
        </if>
        ORDER BY CASE when usr.userclass=5 OR usr.userclass=6 THEN 1 ELSE 2 end ,basket_id
    </select>

    <select id="getSaleOrderKcGroupInfo" resultType="com.jiuji.oa.oacore.weborder.vo.SaleOrderKcGroupInfoVO">
        SELECT
        sub.sub_mobile mobile,
        b.basket_id basketId,
        usr.userclass userClass,
        wu.openid openId,
        b.basket_date basketDate,
        sub.sub_id as orderId
        FROM
        basket b with(nolock)
        INNER JOIN sub with(nolock) ON sub.sub_id = b.sub_id
        INNER JOIN BBSXP_Users usr with(nolock) ON usr.ID = sub.userid
        LEFT JOIN WeixinUser wu with(nolock) ON wu.userid = usr.ID and wu.type = 1 and wu.kinds = 1 and wu.wxid=1 and
        wu.follow = 1
        <where>
            <if test="level != null">
                <if test="level == 1">
                    and usr.userclass in (5,6)
                </if>
                <if test="level == 2">
                    and usr.userclass not in (5,6)
                </if>
            </if>
            <if test="wxBind != null">
                <if test="wxBind == 1">
                    and wu.openid is not null
                </if>
                <if test="wxBind == 2">
                    and wu.openid is null
                </if>
            </if>
            <if test="basketIds != null and basketIds.size() > 0">
                AND b.basket_id IN
                <foreach item="basketId" collection="basketIds" index="index" open="(" separator="," close=")">
                    #{basketId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getInventoryPreset" resultType="com.jiuji.oa.oacore.weborder.vo.InventoryPresetVO">
        select top 1 usr.id        userId,
                     s.sub_mobile  mobile,
                     b.basket_id   basketId,
                     usr.userclass userClass,
                     wu.openid     openId
        from basket b with (nolock)
                     inner join sub s
                with (nolock)
                on s.sub_id = b.sub_id
                     inner join BBSXP_Users usr
                with (nolock)
                on usr.ID = s.userid
                     left join WeixinUser wu
                with (nolock)
                on wu.userid = usr.ID
                        and wu.[type] = 1 and wu.kinds = 1 and wxid = 1 and wu.follow = 1
        where b.basket_id = #{basketId}
    </select>

    <select id="listProductInfoByBasketId" resultType="com.jiuji.oa.oacore.weborder.bo.ProductInfoBO">
        select b.ppriceid,
               p.productid productId,
               s.userid    userId,
               k.id        mkcId,
               k.kc_check,
               b.sub_id,
               b.basket_id,
               s.areaid    areaId,
               b.basket_count,
               p.costprice costPrice,
               p.product_name,
               p.product_color
        from dbo.basket b with (nolock)
                     join dbo.sub s
                with (nolock)
                on b.sub_id = s.sub_id
                     join dbo.productinfo p
                with (nolock)
                on b.ppriceid = p.ppriceid
                     left join dbo.product_mkc k
                with (nolock)
                on k.basket_id = b.basket_id
        where b.basket_id = #{basketId}
          and s.userId = #{userId}
          and isnull(b.isdel
                      , 0) = 0
          and s.sub_check in (0, 1, 5)
    </select>

    <select id="getProductInfo" resultType="com.jiuji.oa.oacore.weborder.bo.ProductInfoBO">
        select productid productId, product_name productName, product_color productColor, costprice costPrice
        from productinfo with (nolock)
        where ppriceid = #{ppriceid}
    </select>

    <select id="getOrderPrice" resultType="java.math.BigDecimal">
        select isnull(b.price_, 0) - s.youhui1M - s.jidianM + shouxuM + feeM - isnull(coinM, 0) priceM
        from sub s with (nolock)
                     left join (select sum(basket_count * price) price_, sub_id
                                from dbo.basket b with (nolock)
                                where isnull(b.isdel, 0) = 0
                                  and sub_id = #{subId}
                                group by sub_id) b
                on b.sub_id = s.sub_id
        where s.sub_id = #{subId}
    </select>

    <select id="getImNewOrders" resultType="com.jiuji.oa.oacore.weborder.vo.ImOrderVO">
    </select>

    <select id="listMineProductInfoBySubIdAndUserId" resultType="com.jiuji.oa.oacore.weborder.vo.ImOrderVO">
        SELECT s.sub_id AS id,
        s.sub_date AS time,
        p.bpic AS imagePath,
        p.product_name AS name,
        s.yingfuM AS price,
        <!--p.memberprice AS memberPrice-->
        b.price as memberPrice
        FROM dbo.sub s WITH (nolock)
        JOIN dbo.basket b WITH (nolock) ON s.sub_id = b.sub_id
        JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE s.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        <if test="userId != null">
            AND s.userid = #{userId}
        </if>
        and (s.sub_check=4 or ISNULL(b.isdel, 0) = 0)
    </select>

    <select id="listRepairProductInfoBySubIdAndUserId" resultType="com.jiuji.oa.oacore.weborder.vo.ImOrderVO">
        <if test="kind != null and kind == 0 or kind ==1">
            SELECT s.id AS id,
            p.bpic AS imagePath,
            isnull(p.product_name,s.name) AS name,
            s.tradeDate AS time,
            s.feiyong AS price,
            p.memberprice AS memberPrice
            FROM dbo.shouhou s WITH (nolock)
            LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = s.ppriceid
            WHERE 1=1
            <!--        and s.sub_id IN-->
            <!--        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">-->
            <!--            #{subId}-->
            <!--        </foreach>-->
            and s.id in
            <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
                #{subId}
            </foreach>
            <if test="userId != null">
                AND s.userid = #{userId}
            </if>
        </if>
        <if test="kind != null and kind == 2">
            SELECT s.id AS id,
            p.bpic AS imagePath,
            isnull(p.product_name,s.name) AS name,
            s.Buydate AS time,
            s.feiyong AS price,
            p.memberprice AS memberPrice
            FROM dbo.Smallpro s WITH (nolock)
            left join SmallproBill sb WITH (nolock) on s.id = sb.smallproID
            left join basket b WITH (nolock) on sb.basket_id = b.basket_id
            LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
            WHERE 1=1
            and s.id in
            <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
                #{subId}
            </foreach>
            <if test="userId != null">
                AND s.userid = #{userId}
            </if>
        </if>
    </select>

    <select id="listRecoverProductInfoBySubIdAndUserId" resultType="com.jiuji.oa.oacore.weborder.vo.ImOrderVO">
        SELECT s.sub_id AS id,
        p.bpic AS imagePath,
        p.product_name AS name,
        s.dtime AS time,
        isnull(k.inprice,b.price) AS price,
        b.goodsId as goodsId,
        <!--p.memberprice AS memberPrice-->
        isnull(b.price,0) as memberPrice
        FROM dbo.recover_sub s WITH (nolock)
        JOIN dbo.recover_basket b WITH (nolock) ON s.sub_id = b.sub_id
        left join dbo.recover_mkc k WITH (nolock) ON b.id = k.from_basket_id
        left JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE s.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        <if test="userId != null">
            AND s.userid = #{userId}
        </if>
        and (s.sub_check=4 or ISNULL(b.isdel, 0) = 0)
    </select>

    <select id="listSecondHandProductInfoBySubIdAndUserId" resultType="com.jiuji.oa.oacore.weborder.vo.ImOrderVO">
        SELECT rm.sub_id AS id,
        p.bpic AS imagePath,
        p.product_name AS name,
        isnull(rm.tradeDate1,rm.sub_date) AS time,
        rm.yingfuM AS price,
        isnull(k.inprice,b.inprice) AS inPrice,
        isnull(k.id,b.mkc_id2) AS mkcId,
        <!--p.memberprice AS memberPrice-->
        b.price as memberPrice
        FROM dbo.recover_marketInfo rm WITH (nolock)
        JOIN dbo.recover_marketSubInfo b WITH (nolock) ON rm.sub_id = b.sub_id
        left join dbo.recover_mkc k WITH (nolock) ON b.basket_id = k.to_basket_id
        left JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE rm.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        <if test="userId != null">
            AND rm.userid = #{userId}
        </if>
        and (rm.sub_check=4 or ISNULL(b.isdel, 0) = 0)
    </select>

    <select id="listStaffInfoByMobile" resultType="com.jiuji.oa.oacore.weborder.vo.HuiShouProductVO">
        select ch999_id as staffId, ch999_name as staffName,mobile from ch999_user with(nolock) where mobile in
        <foreach collection="mobileList" index="index" item="mobile" separator="," open="(" close=")">
            #{mobile}
        </foreach>
    </select>
    <select id="getWhetherToBuy" resultType="com.jiuji.oa.oacore.weborder.vo.req.WhetherToBuyReq$Res">
        SELECT s.userid userId,1 buy FROM dbo.basket b WITH ( nolock )
        LEFT JOIN dbo.sub s WITH (nolock) ON b.sub_id= s.sub_id
        WHERE
        s.sub_check IN ( 0, 1, 2, 6, 5, 3 )
        AND isnull( b.isdel, 0 ) = 0
        AND s.yifuM> 0
        AND s.sub_date
        BETWEEN #{whetherToBuyReq.sTime} AND #{whetherToBuyReq.eTime}
        AND EXISTS (SELECT ID FROM dbo.BBSXP_Users u WITH (nolock)
        WHERE u.xtenant=#{whetherToBuyReq.xTenant}
        AND u.ID in
        <foreach collection="whetherToBuyReq.userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND u.ID= s.userid)
        AND EXISTS (
        SELECT 1 FROM
        dbo.productinfo p WITH ( nolock )
        WHERE
        b.ppriceid= p.ppriceid
        AND p.cid= 2
        AND p.brandID=#{whetherToBuyReq.brandId})
        group by s.userid
    </select>
    <select id="getMyClientComplete" resultType="java.lang.Integer">
        SELECT u.ID
        FROM dbo.BBSXP_Users u with(nolock)
        WHERE u.ID IN
        <foreach collection="myClientReq.userId" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <if test="flag != null and flag == false">
            AND NOT EXISTS
        </if>
        <if test="flag != null and flag == true">
            AND EXISTS
        </if>
         (SELECT s.userid FROM dbo.sub s with(nolock) WHERE u.ID=s.userid
          AND s.sub_check=3
          AND s.tradeDate1
            BETWEEN CONVERT ( datetime,#{myClientReq.beginTime}, 20 )
            AND CONVERT ( datetime,#{myClientReq.endTime}, 20 )
          )
    </select>
    <select id="getMyClientPurchaseComplete" resultType="java.lang.Integer">
        SELECT s.userid
        FROM dbo.sub s with(nolock)
        LEFT JOIN dbo.basket b with(nolock)
        ON b.sub_id = s.sub_id
        WHERE s.sub_check=3
          AND s.tradeDate1
          BETWEEN CONVERT ( datetime,#{myClientReq.beginTime}, 20 )
          AND CONVERT ( datetime,#{myClientReq.endTime}, 20 )
          AND s.userid IN
          <foreach collection="myClientReq.userId" item="userId" open="(" close=")" separator=",">
             #{userId}
          </foreach>
        AND isnull(b.isdel,0)=0
          AND exists
            (SELECT 1 FROM dbo.productinfo p with(nolock) WHERE b.ppriceid=p.ppriceid AND p.productid=#{commodityId})
        GROUP BY s.userid
        </select>

    <select id="getMyClientIsCompleteByCigarette" resultType="java.lang.Integer">
        SELECT s.userid
        FROM dbo.sub s with(nolock)
        LEFT JOIN dbo.basket b with(nolock)
        ON b.sub_id = s.sub_id
        WHERE s.sub_check=3
          AND s.tradeDate1
            BETWEEN CONVERT ( datetime,#{myClientReq.beginTime}, 20 )
            AND CONVERT ( datetime,#{myClientReq.endTime}, 20 )
          AND s.userid IN
            <foreach collection="myClientReq.userId" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
          AND isnull(b.isdel,0)=0
          AND exists
            (SELECT 1 FROM dbo.productinfo p with(nolock)
            WHERE b.ppriceid=p.ppriceid
            AND p.cid IN
            <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                #{value}
            </foreach>
             )
        GROUP BY  s.userid
    </select>
    <select id="listBindLpPjSubInfo" resultType="com.jiuji.oa.oacore.weborder.vo.BindSubInfoBo">
        SELECT rmi.sub_id bindSubId ,rmi.newSubId subId,2 bindBasketSubType,1 bindType
        from recover_marketInfo rmi with(nolock)
        where exists(select 1 from recover_marketSubInfo msi with(nolock) where msi.type=5 and isnull(msi.isdel,0)=0 and msi.sub_id = rmi.sub_id)
          <choose>
              <when test="businessTypeEnum.name() == 'LP_ORDER'">
                  and rmi.sub_id in
                  <foreach collection="subIds" item="subId" separator="," open="(" close=")">
                      #{subId}
                  </foreach>
              </when>
              <otherwise>
                  and rmi.newSubId in
                  <foreach collection="subIds" item="subId" separator="," open="(" close=")">
                      #{subId}
                  </foreach>
              </otherwise>
          </choose>

    </select>

    <select id="getGoodProductStockByMkcList" resultType="com.jiuji.oa.oacore.weborder.vo.GoodProductStockVO">
        SELECT  a.id AS areaId,k.id AS mkcId,a.area AS areaCode,a.city_name AS cityName,a.cityid AS cityId,a.area_name AS areaName,k.mkc_check AS mkcCheck
        ,a.kind1 AS areaKind,a.authorizeid
        FROM recover_mkc k WITH(NOLOCK)
        LEFT JOIN areainfo a WITH(NOLOCK) ON k.areaid = a.id
        WHERE k.id IN
        <foreach collection="mkcIdList" item="mkcId" open="(" close=")" separator=",">
            #{mkcId}
        </foreach>
    </select>
    <select id="listLiangPinExclusiveCustomerService"
            resultType="com.jiuji.oa.oacore.weborder.vo.Ch999UserServiceVO">
        select
        e.RelateCh999Id ch999_id,
        e.Job job,
        e.sub_id
        from
        ${officeName}..EvaluateScore e with(nolock)
        left join dbo.ch999_user u with(nolock) on
        e.RelateCh999Id = u.ch999_id
        where
        e.sub_id in
        <foreach collection="subIds" index="index" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        and e.type_ = 9
        and e.RelateCh999Id != 0
        and u.iszaizhi = 1
    </select>
    <select id="selectGoodAccessories" resultType="java.lang.Integer">
        SELECT 1
        FROM recover_marketInfo s with (nolock)
        where s.newSubId = #{subId}
          and EXISTS(SELECT 1
            FROM recover_marketSubInfo b with (nolock)
            where b.sub_id = s.sub_id and b.type = 5 and isnull(b.isdel, 0) = 0)

    </select>
    <select id="selectGoodAccessoriesGoodProduct" resultType="java.lang.Integer">
        SELECT 1
        FROM recover_marketInfo s with (nolock)
        where s.sub_id = #{subId}
          and EXISTS(SELECT 1
            FROM recover_marketSubInfo b with (nolock)
            where b.sub_id = s.sub_id and b.type = 5 and isnull(b.isdel, 0) = 0)

    </select>
    <select id="getExchangeCount" resultType="com.jiuji.oa.oacore.weborder.bo.ExchangeCountBO">

        select count(1) as count,0 as type from dbo.shouhou_tuihuan t with(nolock,index(index_shouhouid))
        INNER JOIN dbo.shouhou s with(nolock) on t.shouhou_id = s.id
        INNER JOIN dbo.sub s1 with(nolock) on s1.sub_id = s.sub_id
        where t.tuihuan_kind in (3, 4) and t.check3 is not null
        and datediff(day, t.check3dtime, getdate()) &lt;= 365 AND s1.userid = #{memberId}

        UNION ALL
        select count(1) as count,2 as type from dbo.shouhou_tuihuan t with(nolock,index(index_shouhouid))
        INNER JOIN dbo.shouhou s with(nolock) on t.shouhou_id = s.id
        INNER JOIN basket b with(nolock) on b.basket_id = s.basket_id and b.[type] = 22
        INNER JOIN dbo.sub s1 with(nolock) on s1.sub_id = s.sub_id
        where t.tuihuan_kind in (3, 4) and t.check3 is not null
        and datediff(day, t.check3dtime, getdate()) &lt;= 365 AND s1.userid = #{memberId}

        UNION ALL
        select count(1) as count,3 as type from dbo.shouhou_tuihuan t with(nolock,index(index_shouhouid))
        INNER JOIN dbo.shouhou s with(nolock) on t.shouhou_id = s.id
        INNER JOIN dbo.recover_marketInfo rmi with(nolock) on rmi.sub_id  = s.sub_id
        where t.tuihuan_kind in (3, 4) and t.check3 is not null
        and datediff(day, t.check3dtime, getdate()) &lt;= 365 AND rmi.userid = #{memberId};
    </select>

    <select id="getOrderStatusByList" resultType="com.jiuji.oa.oacore.weborder.vo.res.OrderStateRes">
        SELECT s.sub_id orderId, s.sub_check status, s.yifuM ,max(sy.dtime) as payTime
        FROM sub s with(nolock) LEFT JOIN shouying sy with(nolock)
        on sy.sub_id = s.sub_id
        and sy.shouying_type in ('交易','订金')
        where s.sub_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        GROUP BY s.sub_id,s.sub_check,s.yifuM
    </select>

    <!-- getSubIdByBasketId -->
    <select id="getSubIdByBasketId" resultType="java.lang.Integer">
        SELECT b.sub_id
        FROM basket b with (nolock)
        WHERE b.basket_id = #{basketId}
    </select>
    <select id="selectRetreatCount" resultType="java.lang.Integer">
        select isnull(all_count,0) - isnull(tui_count,0) as leftCount from dbo.shouhou_tuihuan_discount_cfg with(nolock) where getdate() between stime and etime and isnull(is_del,0)=0 and business_kind = 2 and user_id = #{userId}
    </select>
    <select id="listWuliuDiaoboBySubId" resultType="com.jiuji.oa.oacore.weborder.bo.WuliuDiaoboSubBO">
        select w.id wuliuId,
        w.sareaid sendAreaId,
        w.rareaid receiveAreaId,
        w.dtime,
        k.orderid orderId,
        '' diaoboId
        from wuliu w with(nolock)
        left join mkc_toarea m with(nolock) on w.id=m.wuliuid
        left join product_mkc k with(nolock) on k.id=m.mkc_id
        where k.basket_id is not null
        and w.id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        union
        select w.id wuliuId,
        w.sareaid sendAreaId,
        w.rareaid receiveAreaId,
        w.dtime,
        '' orderId,
        s.id diaoboId
        from wuliu w with(nolock)
        left join diaobo_sub s with(nolock) on w.id=s.wuliuid
        left join diaobo_basket k with(nolock) on s.id=k.sub_id
        where k.basket_id is not null
        and w.id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        union
        select w.id wuliuId,
        w.sareaid sendAreaId,
        w.rareaid receiveAreaId,
        w.dtime,
        k.orderid orderId,
        '' diaoboId
        from wuliu w with(nolock)
        left join recover_toarea t with(nolock) on t.wuliuid = w.id
        left join recover_mkc k WITH(nolock) on k.id = t.mkc_id
        where k.to_basket_id is not null
        and w.id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
    </select>
    <select id="listDiaoboBySubId" resultType="com.jiuji.oa.oacore.weborder.bo.DiaoboSubBO">
        select
            s.id subId,
            s.areaid fromAreaId,
            s.toareaid toAreaId,
            s.dtime,
            s.title
        from diaobo_sub s with(nolock)
        where s.id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
    </select>

    <select id="getAlipayInfoById" resultType="com.jiuji.oa.oacore.weborder.vo.OrderByWxNoVO">
        SELECT
            a.id,
            a.orderNum,
            a.payType
        FROM
            alipayInfo a WITH(NOLOCK)
        WHERE a.id = #{outTradeNo}
    </select>
    <select id="getPayItemInfoById" resultType="com.jiuji.oa.oacore.weborder.vo.OrderByWxNoVO">
        SELECT
            p.sub_id as orderNum,
            p.payType
        FROM payItemInfo p WITH(NOLOCK)
        WHERE
            p.payId = #{outTradeNo}
    </select>

    <select id="getSubOrderInfoBySubIds" resultType="com.jiuji.oa.oacore.weborder.vo.OrderItemVO">
        SELECT
            s.sub_id AS id,
            s.sub_date AS time,
            p.ppriceid AS ppriceId,
            p.product_name AS name,
            s.yingfuM AS price,
            b.price as memberPrice,
            1 as payType
        FROM dbo.sub s WITH (nolock)
        JOIN dbo.basket b WITH (nolock) ON s.sub_id = b.sub_id
        JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE s.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        and (s.sub_check=4 or ISNULL(b.isdel, 0) = 0)
    </select>

    <select id="getGoodSubOrderInfoBySubIds" resultType="com.jiuji.oa.oacore.weborder.vo.OrderItemVO">
        SELECT
            rm.sub_id AS id,
            p.ppriceid AS ppriceId,
            p.product_name AS name,
            isnull(rm.tradeDate1,rm.sub_date) AS time,
            rm.yingfuM AS price,
            isnull(k.inprice,b.inprice) AS inPrice,
            isnull(k.id,b.mkc_id2) AS mkcId,
            b.price as memberPrice,
            2 as payType
        FROM dbo.recover_marketInfo rm WITH (nolock)
        JOIN dbo.recover_marketSubInfo b WITH (nolock) ON rm.sub_id = b.sub_id
        left join dbo.recover_mkc k WITH (nolock) ON b.basket_id = k.to_basket_id
        left JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE rm.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        and (rm.sub_check=4 or ISNULL(b.isdel, 0) = 0)
    </select>

    <select id="getRepairSubOrderInfoBySubIds" resultType="com.jiuji.oa.oacore.weborder.vo.OrderItemVO">
        SELECT
            s.id AS id,
            p.ppriceid AS ppriceId,
            isnull(p.product_name,s.name) AS name,
            s.tradeDate AS time,
            s.feiyong AS price,
            p.memberprice AS memberPrice,
            3 as payType
        FROM dbo.shouhou s WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = s.ppriceid
        WHERE 1=1
        and s.id in
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="getSmallSubOrderInfoBySubIds" resultType="com.jiuji.oa.oacore.weborder.vo.OrderItemVO">
        SELECT
            s.id AS id,
            p.ppriceid AS ppriceId,
            isnull(p.product_name,s.name) AS name,
            s.Buydate AS time,
            s.feiyong AS price,
            p.memberprice AS memberPrice,
            4 as payType
        FROM dbo.Smallpro s WITH (nolock)
        left join SmallproBill sb WITH (nolock) on s.id = sb.smallproID
        left join basket b WITH (nolock) on sb.basket_id = b.basket_id
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = b.ppriceid
        WHERE 1=1
        and s.id in
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>
</mapper>
