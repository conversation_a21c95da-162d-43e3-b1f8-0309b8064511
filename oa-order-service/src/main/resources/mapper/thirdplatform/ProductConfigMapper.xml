<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.ProductConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductConfigVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="product_code" property="productCode"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="price_split" property="priceSplit"/>
        <result column="sync_off" property="syncOff"/>
        <result column="gift_off" property="giftOff"/>
        <result column="sync_type" property="syncType"/>
        <result column="sync_ratio" property="syncRatio"/>
        <result column="sync_limit" property="syncLimit"/>
        <result column="sync_first" property="syncFirst"/>
        <result column="order_time" property="orderTime"/>
        <result column="plat_code" property="platCode"/>
        <result column="ismobile1" property="ismobile"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="cid" property="cid"/>
        <result column="costprice" property="costprice"/>
        <result column="leftCount" property="leftCount"/>
        <result column="type" property="type"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="library_lock" property="libraryLock"/>
        <result column="sell_type" property="sellType"/>
        <result column="rule_code" property="ruleCode"/>
        <result column="coupon_price" property="couponPrice"/>
    </resultMap>

    <sql id="columns">
        c.id,c.tenant_code,c.sku_id,c.product_code,c.ppriceid,c.price_split,c.sync_off,ISNULL(c.gift_off,0) as gift_off,
        c.sync_type,c.sync_ratio,c.sync_limit,c.sync_first,c.order_time,c.plat_code,c.type,c.mkc_id,c.library_lock
        ,c.rule_code,c.coupon_price,c.label
    </sql>

    <insert id="saveBatch">
        insert into third_platform_product_config (tenant_code,plat_code,product_code,sku_id,ppriceid,
        price_split,sync_off,gift_off,sync_type,sync_ratio,sync_limit,sync_first,mkc_id,library_lock,type
        ,rule_code,coupon_price,label
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode},#{item.platCode}, #{item.productCode}, #{item.skuId}, #{item.ppriceid},
            #{item.priceSplit},#{item.syncOff},#{item.giftOff}, #{item.syncType}, #{item.syncRatio}, #{item.syncLimit}, #{item.syncFirst}, #{item.mkcId}, #{item.libraryLock}, #{item.type}
            ,#{item.ruleCode}, #{item.couponPrice},#{item.label}
            )
        </foreach>
    </insert>

    <select id="productConfigList" resultMap="BaseResultMap">
        SELECT<include refid="columns"/>,t.tenant_name,p.product_name,p.product_color,p.ismobile1,ca.Name as cid,p.costprice,p.memberprice vipPrice,p.que,c.platform_cost
        FROM third_platform_product_config c WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=c.ppriceid
        LEFT JOIN third_platform_tenant t WITH(NOLOCK) ON t.tenant_code=c.tenant_code
        LEFT JOIN category ca WITH(NOLOCK) ON ca.id=p.cid
        <where>
            and isnull(c.is_del,0) = 0
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND c.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.platCode != null and search.platCode != ''">
                AND c.plat_code = #{search.platCode}
            </if>
            <if test="search.syncOff != null and search.syncOff ==1">
                AND c.sync_off = 1
            </if>
            <if test="search.syncOff != null and search.syncOff ==0">
                AND c.sync_off = 0
            </if>
            <if test="search.giftOff != null and search.giftOff ==1">
                AND ISNULL(c.gift_off,0) = 1
            </if>
            <if test="search.giftOff != null and search.giftOff ==0">
                AND ISNULL(c.gift_off,0) = 0
            </if>
            <!--判断抖店的商品类型-->
            <if test="search.type != null">
                AND c.type = #{search.type}
            </if>
            <!--判断配置标签-->
            <if test="search.label != null">
                <choose>
                    <when test="search.label == 5">AND c.label is null</when>
                    <otherwise>AND c.label = #{search.label}</otherwise>
                </choose>
            </if>
            <if test="search.que != null">
                AND p.que = #{search.que}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND c.sku_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 2">
                        <if test="search.type == null or search.type == 0">
                            AND c.ppriceid = #{search.searchValue}
                        </if>
                        <if test="search.type != null and search.type == 1">
                            AND c.mkc_id = #{search.searchValue}
                        </if>
                    </when>
                    <when test="search.searchOptions == 3">
                        AND p.product_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 4">
                        and c.product_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectListWithStock" resultMap="BaseResultMap">
         SELECT c.*,k.leftCount FROM third_platform_store s WITH(NOLOCK)
                INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.areaid=s.area_id
                INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                WHERE s.store_code=#{storeCode} AND c.sync_off=1 and isnull(c.is_del,0) = 0
                UNION ALL
                SELECT c.*,k.leftCount FROM third_platform_store s WITH(NOLOCK)
                INNER JOIN (
                    SELECT k.areaid, k.ppriceid,COUNT(*) leftCount FROM dbo.product_mkc k WITH(NOLOCK) WHERE kc_check=3 AND isnull(k.mouldFlag,0)=0
                     AND NOT EXISTS(SELECT 1 FROM dbo.xc_mkc xc WITH(NOLOCK) WHERE xc.mkc_id=k.id AND isnull(xc.isLock,0)=1 )
                     GROUP BY k.areaid,k.ppriceid
                ) k ON k.areaid=s.area_id
                INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                WHERE s.store_code=#{storeCode} AND c.sync_off=1 and isnull(c.is_del,0) = 0
    </select>

    <select id="selectListWithStockV1" resultMap="BaseResultMap">
        SELECT c.*,k.leftCount,s.store_code as storeCode FROM third_platform_store s WITH(NOLOCK)
        INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.areaid=s.area_id
        INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid and c.plat_code = s.plat_code and c.tenant_code = s.tenant_code
        WHERE s.store_code in
        <foreach collection="storeCodes" index="index" item="storeCode" separator="," open="(" close=")">
            #{storeCode}
        </foreach>
        AND c.sync_off=1 and isnull(c.is_del,0) = 0
        <if test="platCode != null and platCode != ''">
            and c.plat_code = #{platCode}
            and isnull(c.type, 0) = 0
        </if>
        <if test="ppids != null and !ppids.isEmpty()">
            and k.ppriceid in
            <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                #{ppid}
            </foreach>
        </if>
        UNION ALL
        SELECT c.*,k.leftCount,s.store_code as storeCode FROM third_platform_store s WITH(NOLOCK)
        INNER JOIN (
        SELECT k.areaid, k.ppriceid,COUNT(*) leftCount FROM dbo.product_mkc k WITH(NOLOCK)
        WHERE k.basket_id is NULL AND kc_check=3 AND isnull(k.mouldFlag,0)=0 and k.basket_id is null
        AND NOT EXISTS(SELECT 1 FROM dbo.xc_mkc xc WITH(NOLOCK) WHERE xc.mkc_id=k.id AND isnull(xc.isLock,0)=1 )
        GROUP BY k.areaid,k.ppriceid
        ) k ON k.areaid=s.area_id
        INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid and c.plat_code = s.plat_code and c.tenant_code = s.tenant_code
        WHERE s.store_code in
        <foreach collection="storeCodes" index="index" item="storeCode" separator="," open="(" close=")">
            #{storeCode}
        </foreach>
        AND c.sync_off=1 and isnull(c.is_del,0) = 0
        <if test="platCode != null and platCode != ''">
            and c.plat_code = #{platCode}
            and isnull(c.type, 0) = 0
        </if>
        <if test="ppids != null and !ppids.isEmpty()">
            and k.ppriceid in
            <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                #{ppid}
            </foreach>
        </if>
    </select>

    <select id="selectListWithStockByJiuJi"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductLeftCountBO">
        SELECT c.id, isnull(SUM(k.leftCount),0) leftCount,s.store_code as storeCode,s.plat_code platCode,s.tenant_code tenantCode
        FROM third_platform_store s WITH(NOLOCK)
        INNER JOIN third_platform_product_config c WITH( NOLOCK) ON s.plat_code = c.plat_code and s.tenant_code = c.tenant_code and c.sync_off = 1
        inner join productinfo p with(nolock) on p.ppriceid = c.ppriceid and p.ismobile1 = 0
        LEFT JOIN dbo.product_kc k WITH(NOLOCK) ON c.ppriceid = k.ppriceid and k.areaid in
        <foreach collection="areaId" index="index" item="aid" separator="," open="(" close=")">
            #{aid}
        </foreach>
        WHERE s.store_code = #{storeCode} and isnull(c.is_del,0) = 0
        <if test="ppids != null and !ppids.isEmpty()">
            and c.ppriceid in
            <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                #{ppid}
            </foreach>
        </if>
        GROUP BY c.id,s.store_code,s.plat_code,s.tenant_code
        UNION ALL
        SELECT c.id, isnull(SUM(k.leftCount),0) leftCount,s.store_code as storeCode,s.plat_code platCode,s.tenant_code tenantCode
        FROM third_platform_store s WITH(NOLOCK)
        INNER JOIN third_platform_product_config c WITH( NOLOCK) ON s.plat_code = c.plat_code and s.tenant_code = c.tenant_code and c.sync_off = 1
        inner join productinfo p with(nolock) on p.ppriceid = c.ppriceid and p.ismobile1 = 1
        left JOIN( SELECT k.areaid, k.ppriceid, COUNT( * ) leftCount FROM dbo.product_mkc k WITH(NOLOCK)
                WHERE kc_check = 3 AND isnull(k.mouldFlag,0) = 0 and k.basket_id is null
                  AND NOT EXISTS( SELECT 1 FROM dbo.xc_mkc xc WITH(NOLOCK) WHERE xc.mkc_id = k.id AND isnull(xc.isLock, 0)= 1)
                <if test="ppids != null and !ppids.isEmpty()">
                    and k.ppriceid in
                    <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                        #{ppid}
                    </foreach>
                </if>
                <choose>
                    <when test="localAreaId != null">
                        and k.areaid = #{localAreaId}
                    </when>
                 <otherwise>
                        and k.areaid in
                        <foreach collection="areaId" index="index" item="aid" separator="," open="(" close=")">
                            #{aid}
                        </foreach>
                    </otherwise>
                </choose>
            GROUP BY k.areaid, k.ppriceid
            ) k ON c.ppriceid = k.ppriceid
        WHERE s.store_code = #{storeCode} and isnull(c.is_del,0) = 0
        <if test="ppids != null and !ppids.isEmpty()">
            and c.ppriceid in
            <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                #{ppid}
            </foreach>
        </if>
        GROUP BY c.id,s.store_code,s.plat_code,s.tenant_code
    </select>

    <select id="productConfigListByIds" resultMap="BaseResultMap">
        SELECT <include refid="columns"/> FROM third_platform_product_config c WITH(NOLOCK) where c.id in
        <foreach collection="productLeftCounts" index="index" item="productLeftCount" separator="," open="(" close=")">
            #{productLeftCount.id}
        </foreach>
    </select>

    <select id="productConfigListVo" resultMap="BaseResultMap">
        SELECT <include refid="columns"/> FROM third_platform_product_config c WITH(NOLOCK) where isnull(c.sync_off, 0) = 1 and isnull(c.type, 0) = 0 and isnull(c.is_del,0) = 0
        <if test="platCode != null and platCode != ''">
            and c.plat_code = #{platCode}
        </if>
        <if test="ppids != null and !ppids.isEmpty()">
            and c.ppriceid in
            <foreach collection="ppids" separator="," item="ppid" open="(" close=")">
                #{ppid}
            </foreach>
        </if>
        <if test="tenantCodes != null and !tenantCodes.isEmpty()">
            and c.tenant_code in
            <foreach collection="tenantCodes" separator="," item="tenantCode" open="(" close=")">
                #{tenantCode}
            </foreach>
        </if>
    </select>

    <select id="getProductConfigListByMkcIdList"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig">
        SELECT <include refid="columns"/> FROM third_platform_product_config c WITH(NOLOCK)
        where c.tenant_code = #{tenantCode} and c.plat_code = #{platCode} and c.mkc_id in
        <foreach collection="mkcIdList" index="index" item="mkcId" separator="," open="(" close=")">
            #{mkcId}
        </foreach>
        and isnull(c.is_del,0) = 0
    </select>

    <select id="checkSkuIdList" resultType="java.lang.Integer">
        SELECT count(1) FROM third_platform_product_config c WITH(NOLOCK) where isnull(c.sync_off, 0) = 1 and c.plat_code = 'DY' and isnull(c.is_del,0) = 0
        and c.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>

    <update id="lockStockBatch">
        UPDATE recover_mkc SET order_limit= #{type}
        <where>
            <if test="type == 1">
                (
                to_basket_id is null
                <if test="toBasketIds != null and !toBasketIds.isEmpty()">
                    <foreach collection="toBasketIds" open="or to_basket_id in(" close=")" separator="," item="toBasketId">
                        #{toBasketId}
                    </foreach>
                </if>
                )
            </if>
            and id in
            <foreach item="mkcId" index="index" collection="mkcIdList" open="(" separator="," close=")">
                #{mkcId}
            </foreach>
        </where>

    </update>

    <update id="updateSellType">
        UPDATE third_platform_product_config SET sell_type= #{sellType}  WHERE
        mkc_id in
        <foreach item="mkcId" index="index" collection="mkcIds" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
    </update>

    <select id="getPriceByMkcId" resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.RecoverMkcBO">
        SELECT mkc_id AS mkcId,gSalePirce AS salePirce FROM salfGoods sa WITH(nolock) WHERE
        mkc_id in
        <foreach item="mkcId" index="index" collection="mkcIds" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
    </select>

    <select id="getInpriceFromProductKc" resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductKcInpriceVO">
        select ppriceid, AVG(inprice) as inprice
        from product_kc pk with(nolock)
        where ppriceid is not null and ppriceid in
        <foreach item="ppid" index="index" collection="ppidList" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        group by ppriceid

    </select>
    <select id="selectListWithStockByJiuJiV2"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductLeftCountV2BO">
        SELECT k.ppriceid, k.leftCount leftCount,k.areaid as areaId
        FROM dbo.product_kc k WITH(NOLOCK)
        where k.areaid in
        <foreach collection="areaIds" index="index" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        <if test="ppids != null">
            <foreach collection="ppids" index="index" item="ppid" separator="," open="and k.ppriceid in (" close=")">
                #{ppid}
            </foreach>
        </if>
        UNION ALL
        SELECT k.ppriceid, k.leftCount leftCount,k.areaid as areaId
        FROM (
            SELECT k.areaid, k.ppriceid, COUNT( * ) leftCount FROM dbo.product_mkc k WITH(NOLOCK) WHERE kc_check = 3
                AND isnull(k.mouldFlag,0) = 0 AND NOT EXISTS( SELECT 1 FROM dbo.xc_mkc xc WITH(NOLOCK) WHERE xc.mkc_id = k.id AND isnull(xc.isLock, 0)= 1 )
                and k.areaid in
                <foreach collection="areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                <if test="ppids != null">
                    <foreach collection="ppids" index="index" item="ppid" separator="," open="and k.ppriceid in (" close=")">
                        #{ppid}
                    </foreach>
                </if>
                GROUP BY k.areaid, k.ppriceid
            ) k
    </select>
    <select id="productConfigListV2"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductConfigVOV2">
        SELECT c.id,c.tenant_code,c.sku_id,c.order_time,c.plat_code,c.type,
        case when c.type = 0 then '销售'
            when c.type = 1 then '良品'
            when c.type = 2 then '小件售后'
            when c.type = 3 then '维修' else '' end typeName,
        c.actual_payment_type,
        c.rule_code,c.coupon_price,t.tenant_name,c.product_name,c.discount_amount ,c.service_ppid
        FROM third_platform_product_config c WITH(NOLOCK)
        LEFT JOIN third_platform_tenant t WITH(NOLOCK) ON t.tenant_code=c.tenant_code
        <where>
            and isnull(c.is_del,0) = 0
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND c.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.platCode != null and search.platCode != ''">
                AND c.plat_code = #{search.platCode}
            </if>
            <!--判断抖店的商品类型-->
            <if test="search.type != null">
                AND c.type = #{search.type}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1 ">
                        AND c.product_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 2">
                        AND c.rule_code = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 3">
                        AND c.sku_id = #{search.searchValue}
                    </when>

                    <when test="search.searchOptions == 4">
                        and #{search.searchValue} in (SELECT * from dbo.F_SPLIT(isnull(c.service_ppid, ''), ','))
                    </when>
                </choose>
            </if>
        </where>
    </select>
    <select id="listProductConfigs" resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ListProductConfigsRes">

        SELECT sku_id as skuId, ppriceid as ppid
        FROM dbo.third_platform_product_config with(nolock)
        WHERE tenant_code = 'ALL_MI' and isnull(is_del,0)=0
        <if test="req.time != null">
            AND (create_time >= #{req.time} OR update_time >= #{req.time})
        </if>


    </select>



</mapper>
