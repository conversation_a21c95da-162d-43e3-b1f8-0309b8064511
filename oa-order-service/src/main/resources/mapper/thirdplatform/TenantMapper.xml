<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.tenant.mapper.TenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="app_key" property="appKey"/>
        <result column="app_secret" property="appSecret"/>
        <result column="is_enable" property="isEnable"/>
        <result column="UserId" property="userId"/>
        <result column="plat_kemu" property="platKemu"/>
        <result column="vender_kemu" property="venderKemu"/>
        <result column="refund_kemu" property="refundKemu"/>
        <result column="orderSwitch" property="orderSwitch"/>
        <result column="plat_code" property="platCode"/>
        <result column="app_type" property="appType"/>
        <result column="retail_account" property="retailAccount"/>
        <result column="retail_password" property="retailPassword"/>
        <result column="retail_pc_token" property="retailPcToken"/>
        <result column="retail_app_token" property="retailAppToken"/>
        <result column="retail_company_id" property="retailCompanyId"/>
        <result column="customer_level_rebate" property="customerLevelRebate"/>
    </resultMap>

    <sql id="columns">
        t.id,t.tenant_code,t.tenant_name,t.app_key,t.app_secret,t.is_enable,t.UserId,t.plat_kemu,t.vender_kemu,t.refund_kemu,t.orderSwitch,t.plat_code,t.basket_type_gift,t.app_type,
        t.retail_account,t.retail_password,t.retail_pc_token,t.retail_app_token,t.retail_company_id,t.customer_level_rebate
    </sql>

    <select id="tenantList" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"/>
        FROM third_platform_tenant t WITH(NOLOCK)
        <where>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND t.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.platCode != null and search.platCode != ''">
                AND t.plat_code = #{search.platCode}
            </if>
            <if test="search.platCodeList!=null and search.platCodeList.size() > 0">
                AND t.plat_code IN
                <foreach collection="search.platCodeList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="search.tenantName != null and search.tenantName != ''">
                AND t.tenant_name LIKE concat('%',#{search.tenantName},'%')
            </if>
        </where>
    </select>
    <select id="getTenantByStore" resultType="com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant">
        SELECT
        <include refid="columns"/>
        FROM third_platform_tenant t WITH(NOLOCK)
        inner join third_platform_store tps with(nolock) on tps.tenant_code = t.tenant_code and tps.plat_code = t.plat_code
        where t.is_enable = 1 and tps.is_enable = 1 and tps.store_code = #{storeCode} and t.plat_code = #{platCode}
    </select>
    <select id="getTenantByPlatCodeAndAreaId"
            resultType="com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant">
        SELECT
        top 1
        <include refid="columns"/>
        FROM third_platform_tenant t WITH(NOLOCK)
        inner join third_platform_store tps with(nolock) on tps.tenant_code = t.tenant_code and tps.plat_code = t.plat_code
        where t.is_enable = 1 and tps.is_enable = 1 and tps.area_id = #{areaId} and t.plat_code = #{platCode}
    </select>
    <select id="getPaymentConfig" resultType="java.lang.String">
        SELECT c.name
        FROM dbo.sysConfig c with(nolock)
        LEFT JOIN payment_config cfg WITH(NOLOCK) ON cfg.sys_config_id=c.id
        <where>
            <if test="req.code != null and req.code != '' ">
                and c.code = #{req.code}
            </if>
            <if test="req.authorizeId != null and req.authorizeId != '' ">
                and c.authId = #{req.authorizeId}
            </if>
            <if test="req.searchKeyWord != null and req.searchKeyWord != '' ">
                and c.name like concat('%',#{req.searchKeyWord},'%')
            </if>
        </where>
    </select>
    <select id="getTenantByPlatCodeAndAppkey"
            resultType="com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant">
        SELECT
        top 1
        <include refid="columns"/>
        FROM third_platform_tenant t WITH(NOLOCK)
        where t.is_enable = 1 and t.plat_code = #{platCode}
        <if test="appKey != null and appKey != '' ">
            and t.app_key = #{appKey}
        </if>
    </select>
    <select id="getTenantStoreByPlatCodeAndAreaId"
            resultType="com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto">
        SELECT
        top 1
        t.tenant_code tenantCode,t.tenant_name tenantName,t.app_key appKey,t.app_secret appSecret,
               tps.store_code storeCode,a.id areaId,a.area area
        FROM third_platform_tenant t WITH(NOLOCK)
        inner join third_platform_store tps with(nolock) on tps.tenant_code = t.tenant_code and tps.plat_code = t.plat_code
        left join areainfo a with(nolock) on tps.area_id = a.id
        where t.is_enable = 1 and tps.is_enable = 1 and tps.area_id = #{areaId} and t.plat_code = #{platCode}
    </select>
    <select id="getTenantStore" resultType="com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto">
        SELECT
            top 1
        t.tenant_code tenantCode,t.tenant_name tenantName,t.app_key appKey,t.app_secret appSecret,
            tps.store_code storeCode,tps.store_name storeName,a.id areaId,a.area area
        FROM third_platform_tenant t WITH(NOLOCK)
        inner join third_platform_store tps with(nolock) on tps.tenant_code = t.tenant_code and tps.plat_code = t.plat_code
            left join areainfo a with(nolock) on tps.area_id = a.id
        where t.is_enable = 1 and tps.is_enable = 1
            <if test="req.platCode != null and req.platCode != '' ">
                and t.plat_code = #{req.platCode}
            </if>
            <if test="req.areaId != null and req.areaId != '' ">
                and tps.area_id = #{req.areaId}
            </if>
            <if test="req.appType != null and req.appType != '' ">
                and isnull(t.app_type,0) = #{req.appType}
            </if>
    </select>
    <select id="getTenantByPlatCodeAndAppkeyV2"
            resultType="com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant">
        SELECT
        top 1
        <include refid="columns"/>
        FROM third_platform_tenant t WITH(NOLOCK)
        where t.plat_code = #{platCode}
        and t.app_key = #{appKey}
    </select>

</mapper>
