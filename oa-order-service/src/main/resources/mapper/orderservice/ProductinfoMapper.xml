<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ProductinfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Productinfo">
        <id column="ppriceid" property="ppriceid"/>
        <result column="productid" property="productid"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="costprice" property="costprice"/>
        <result column="vipPrice" property="vipPrice"/>
        <result column="cid" property="cid"/>
        <result column="ismobile1" property="ismobile1"/>
        <result column="bpic" property="bpic"/>
        <result column="memberprice" property="memberprice"/>
        <result column="pricefd" property="pricefd"/>
        <result column="que" property="que"/>
        <result column="display" property="display"/>
        <result column="isdel" property="isdel"/>
        <result column="viewsWeek" property="viewsWeek"/>
        <result column="ppriceid1" property="ppriceid1"/>
        <result column="config" property="config"/>
        <result column="brandID" property="brandID"/>
        <result column="cidFamily" property="cidFamily"/>
        <result column="viewsweekr" property="viewsweekr"/>
        <result column="rank1" property="rank1"/>
        <result column="noPromotion" property="noPromotion"/>
        <result column="pLabel" property="pLabel"/>
        <result column="OEMPrice" property="OEMPrice"/>
        <result column="barCode" property="barCode"/>
        <result column="vip2price" property="vip2price"/>
        <result column="isbarCode" property="isbarCode"/>
        <result column="Scarcity" property="scarcity"/>
    </resultMap>

    <resultMap id="SearchResultMap" type="com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO">
        <result column="id" property="id"/>
        <result column="to_basket_id" property="toBasketId"/>
        <result column="ppriceid" property="ppid"/>
        <result column="pLabel" property="label"/>
        <result column="name" property="productName"/>
        <result column="spec" property="productColor"/>
    </resultMap>

    <select id="getProductInfoByImei" resultType="com.jiuji.oa.oacore.oaorder.res.ProductInfoSimpleVo">
            SELECT TOP
        1 t.*
    FROM
        (
        SELECT
            p.productid AS productId,
            p.ppriceid AS ppid,
            p.product_name AS productName
        FROM
            productinfo p WITH (nolock)
            INNER JOIN product_mkc mkc WITH (nolock) ON p.ppriceid= mkc.ppriceid
            AND mkc.imei= #{imei}
            UNION ALL
        SELECT
            p.productid AS productId,
            k.ppriceid AS ppid,
            p.product_name AS productName
        FROM
            dbo.recover_mkc k WITH (NOLOCK)
            INNER JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE
            k.imei = #{imei}
        )t
    </select>

    <select id="getProductMapByPpids" resultType="com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO">
        SELECT
        p.productid AS pid,
        p.ppriceid AS ppid,
        p.product_color,
        p.product_name,
        p.memberprice,
        p.costprice,
        p.cid,
        p.ismobile1 as isMobile,
        p.brandID as brandId
        FROM
        productinfo p WITH (NOLOCK)
        where 1=1
        <if test="ppids != null and ppids.size>0">
            and p.ppriceid in
            <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>
    </select>


    <select id="getProductMapByProductIds" resultType="com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO">
        SELECT
        p.productid AS pid,
        p.ppriceid AS ppid,
        product_color,
        product_name,
        p.cid,
        p.ismobile1 as isMobile,
        p.brandID as brandId
        FROM
        productinfo p WITH (NOLOCK)
        where 1=1
        <if test="productIds != null and productIds.size>0">
            and p.productid in
            <foreach collection="productIds" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>
    </select>

    <select id="getNameByIds" resultType="com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO">
        select ppriceid id,product_name+isnull(product_color,'') Name from dbo.productinfo with(nolock) where ppriceid
        in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getProducId" resultType="java.lang.Integer">
        select ID from dbo.product with(nolock) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getProductpriceId" resultType="java.lang.Integer">
        select ppriceid from dbo.productprice with(nolock) where ppriceid in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getTop5SmallProductByNameLike"
            resultMap="SearchResultMap">
        SELECT TOP 10
            pi.ppriceid ,
	        pi.pLabel ,
	        p.name,
	        ISNULL(pi.product_color, '') as spec
        FROM
	        dbo.product p with (nolock)
	        left join dbo.productinfo pi with (nolock)
	        on p.id=pi.product_id
        WHERE
        	p.display = 1
        	AND ismobile = 0
        	AND p.name LIKE CONCAT ( '%',#{name}, '%' )
        	AND NOT EXISTS ( SELECT 1 FROM dbo.f_category_children ( 23 ) f with (nolock) WHERE f.ID= p.cid )
        ORDER BY
	        p.viewsWeek DESC
    </select>
    <select id="getTop5SmallProductByIdOrLike"
            resultMap="SearchResultMap">
        SELECT TOP 10
            pi.ppriceid ,
	        pi.pLabel ,
	        p.name,
	        ISNULL(pi.product_color, '') as spec
        FROM
	        dbo.product p with (nolock)
	        left join dbo.productinfo pi with (nolock)
	        on p.id=pi.product_id
        WHERE
        	p.display = 1
        	AND ismobile = 0
        	AND p.name LIKE CONCAT ( '%',#{id}, '%' )
        	AND NOT EXISTS ( SELECT 1 FROM dbo.f_category_children ( 23 ) f with (nolock) WHERE f.ID= p.cid )
        	OR pi.ppriceid = CAST(#{id} as int)
        ORDER BY
	        p.viewsWeek DESC
    </select>
    <select id="listProductInfoByBrandId" resultType="com.jiuji.oa.oacore.brand.dji.vo.ProductInfoVO">
        SELECT product_name prodName,barCode prodBarCode,ppriceid SKUID,'台' prodUnit from productinfo with(nolock) where brandID=#{brandId}
    </select>

    <select id="getProductNamesByPpids" resultMap="SearchResultMap">
        SELECT memberprice,ppriceid, product_name  name,product_color spec FROM dbo.productinfo with(nolock) WHERE ppriceid in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getProductListByPpids" resultMap="BaseResultMap">
        select * from productinfo with(nolock) where 1=1
        <if test="ppids != null and ppids.size > 0">
            and ppriceid in
            <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>

    </select>

    <select id="listProductNamesOnlyByMkcId" resultMap="SearchResultMap">
        SELECT rm.id,rm.ppriceid,rm.to_basket_id
        from recover_mkc rm with(nolock)
        where rm.id in
        <foreach collection="mkcIds" index="index" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
    </select>

    <select id="getProductNamesByMkcId" resultMap="SearchResultMap">
        SELECT rm.id,rm.ppriceid,rm.to_basket_id,p.product_name name,p.product_color spec
        from recover_mkc rm with(nolock)
        inner join productinfo p with (nolock) on p.ppriceid  = rm.ppriceid
        where rm.mkc_check in (3,8,10) and rm.issalf = 1
          <if test="isToBasketIdNull">
              and rm.to_basket_id is NULL
          </if>
          and rm.id in
        <foreach collection="mkcIds" index="index" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
    </select>
    <select id="getProductIsmobileByPpids" resultType="com.jiuji.oa.oacore.oaorder.bo.ProductIsMobileInfoBO">
        select ppriceid ppid,ismobile1 ismobile from productinfo with(nolock)
        where ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
    </select>
    <select id="getProductMkcCountByPpids" resultType="com.jiuji.oa.oacore.weborder.vo.res.StockCountVO">
        select ppriceid,areaid,kind,COUNT(1) as count into #mkc from
        (select ppriceid,(case when kc_check =10 then 2 else 1 end) as kind,areaid from product_mkc k with(nolock) where
        kc_check in(2,3,10) and isnull(mouldFlag,0)=0 and basket_id is null and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1  )
        and k.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        <if test="req.areaIds != null and req.areaIds.size > 0">
            and k.areaid in
            <foreach collection="req.areaIds" index="index" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        ) t
        group by ppriceid,kind,areaid;

        select * into #prolimit from (
        select r.productid,f.split_value,1 as type from dbo.rareProductConfig r with(nolock) outer apply dbo.F_SPLIT(r.limitArea,',') f where r.buyLimit=1
        union
        select r.productid,f.split_value,2 as type from dbo.rareProductConfig r with(nolock) outer apply dbo.F_SPLIT(r.limitArea,',') f where r.buyLimit=0
        ) f;

        select k.*,p.cid from (
        select m.*,a.area from #mkc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id where m.count &gt; 0 and not exists(select 1 from #prolimit f where m.ppriceid=f.productid  and isnull(f.split_value,'') &lt;&gt; '' and left(a.cityid, len(f.split_value)) = f.split_value) and not exists(select 1 from dbo.rareProductConfig r with(nolock) where r.buyLimit=0 and m.ppriceid=r.productid and isnull(r.limitArea,'')='')
        union
        select m.*,a.area from #mkc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id
        where m.count &gt; 0 and exists(select 1 from #prolimit f where m.ppriceid=f.productid and f.type=1 and len(f.split_value) &gt; 0 and left(a.cityid,len(f.split_value))=f.split_value)
        union
        select m.*,a.area from #mkc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id
        where m.count &gt; 0 and exists(select 1 from #prolimit f where m.ppriceid=f.productid and f.type=2 and isnull(f.split_value,'') &lt;&gt; '')
        and not exists(select 1 from #prolimit f where m.ppriceid=f.productid and f.type=2 and len(f.split_value) &gt; 0 and left(a.cityid,len(f.split_value))=f.split_value)
        )k left join dbo.productinfo p on p.ppriceid = k.ppriceid
        drop table #mkc,#prolimit
    </select>
    <select id="getProductKcCountByPpids" resultType="com.jiuji.oa.oacore.weborder.vo.res.StockCountVO">
        select * into #rare_prolimit from (
        select r.productid,f.split_value,1 as type from dbo.rareProductConfig r with(nolock) outer apply dbo.F_SPLIT(r.limitArea,',') f where r.buyLimit=1
        union
        select r.productid,f.split_value,2 as type from dbo.rareProductConfig r with(nolock) outer apply dbo.F_SPLIT(r.limitArea,',') f where r.buyLimit=0
        ) f;

        select k.*,1 kind from (
        select ppriceid,leftCount as count,areaid,a.area from dbo.product_kc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id where m.leftCount &gt; 0 and not exists(select 1 from #rare_prolimit f where m.ppriceid=f.productid) and not exists(select 1 from dbo.rareProductConfig r with(nolock) where r.buyLimit=0 and m.ppriceid=r.productid and isnull(r.limitArea,'')='')
        and m.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        union
        select ppriceid,leftCount as count,areaid,a.area from dbo.product_kc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id
        where m.leftCount &gt; 0 and exists(select 1 from #rare_prolimit f where m.ppriceid=f.productid and f.type=1 and left(a.cityid,len(f.split_value))=f.split_value)
        and m.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        union
        select ppriceid,leftCount as count,areaid,a.area from dbo.product_kc m with(nolock) left join dbo.areainfo a with(nolock) on m.areaid=a.id
        where  m.leftCount &gt; 0 and exists(select 1 from #rare_prolimit f where m.ppriceid=f.productid and f.type=2)
        and not exists(select 1 from #rare_prolimit f where m.ppriceid=f.productid and f.type=2 and left(a.cityid,len(f.split_value))=f.split_value)
        and m.ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        )k
        where 1 = 1
        <if test="req.areaIds != null and req.areaIds.size > 0">
            and k.areaid in
            <foreach collection="req.areaIds" index="index" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        drop table #rare_prolimit
    </select>
    <select id="getProductMkcStockByPpids" resultType="com.jiuji.oa.oacore.weborder.res.WebStockInfoResVO">
        select * into #mkc_guobu from (
        select pm.mkc_id,row_number() over (partition by pm.mkc_id order by pm.create_time desc) as rn from product_mkc_guobu_info pm with(nolock)
        where DATEADD(hour, 48, pm.create_time)>GETDATE() and pm.is_del=0
        ) f;

        select ppriceid ppid,areaid,sum(kcCount) kcCount,sum(ztCount) ztCount,sum(ydCount) ydCount,
        sum(gbkc) gbkc from
        (select ppriceid,
        (case when kc_check in (2,3) then 1 else 0 end) as kcCount,
        (case when kc_check in (10)  then 1 else 0 end) as ztCount,
        (case when m.mkc_id is not null then 1 else 0 end) as ydCount,
        (case when gb.mkc_id is null then 1 else 0 end) as gbkc,
        areaid
        from product_mkc k with(nolock)
        left join office.dbo.mobileMkc m with(nolock) on k.id=m.mkc_id and m.kind = 2
        left join areainfo a with(nolock) on k.areaid = a.id
        left join #mkc_guobu gb with(nolock) on k.id = gb.mkc_id
        where kc_check in(2,3,10)
        and imei is not null
        and isnull(mouldFlag,0)=0
        and basket_id is null
        and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1)
        <if test="req.xtenant != null">
            and a.xtenant = #{req.xtenant}
        </if>
        and k.ppriceid in
        <foreach collection="req.ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        ) t
        group by ppriceid,areaid
        drop table #mkc_guobu
    </select>
    <select id="getProductKcStockByPpids" resultType="com.jiuji.oa.oacore.weborder.res.WebStockInfoResVO">
        select t.ppid,t.areaid,sum(kcCount) kcCount,sum(ztCount) ztCount,sum(ydCount) ydCount from (
        select k.ppriceid ppid,leftCount as kcCount,0 ztCount,0 ydCount,areaid
        from dbo.product_kc k with(nolock)
        left join areainfo a with(nolock) on k.areaid = a.id
        where k.leftCount >= 0
        <if test="req.xtenant != null">
            and a.xtenant = #{req.xtenant}
        </if>
        and k.ppriceid in
        <foreach collection="req.ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        union
        select db.ppriceid ppid,0 kcCount,sum(isnull(lcount,0))-sum(isnull(InStockCount,0)) ztCount,0 ydCount,toareaid areaid from diaobo_sub ds with(nolock)
        left join diaobo_basket db with(nolock) on ds.id = db.sub_id
        left join areainfo a with(nolock) on ds.toareaid = a.id
        where db.basket_id is null
        and ds.stats in (2,3,5,6)
        <if test="req.xtenant != null">
            and a.xtenant = #{req.xtenant}
        </if>
        and db.ppriceid in
        <foreach collection="req.ppids" index="index" item="ppid" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        group by db.ppriceid,ds.toareaid
        ) t group by t.ppid,t.areaid
    </select>
    <select id="getDiaoboByPpidAndToArea" resultType="com.jiuji.oa.oacore.weborder.res.DiaoboStockInfoResVO">
        <choose>
            <when test="req.isMobile == 1">
                select k.id mkcId,
                k.ppriceid ppid,
                isnull(mt.areaid,k.areaid) areaId,
                isnull(mt.toareaid,k.areaid) toareaId,
                1 lcount,
                mt.sendtime sendTime
                from product_mkc k with(nolock)
                left join mkc_toarea mt with(nolock) on k.id = mt.mkc_id
                where isnull(mouldFlag,0)=0
                and mt.mkc_id is not null
                and basket_id is null
                and imei is not null
                and kc_check in(10)
                and mt.stats in (0,1,2)
                and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1)
                and mt.toareaid = #{req.toareaId}
                and k.ppriceid = #{req.ppid}
                union
                select k.id mkcId,
                k.ppriceid ppid,
                isnull(mt.areaid,k.areaid) areaId,
                isnull(mt.toareaid,k.areaid) toareaId,
                1 lcount,
                mt.sendtime sendTime
                from product_mkc k with(nolock)
                left join mkc_toarea mt with(nolock) on k.id = mt.mkc_id
                where isnull(mouldFlag,0)=0
                and mt.mkc_id is null
                and basket_id is null
                and imei is not null
                and kc_check in(10)
                and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1)
                and k.areaid = #{req.toareaId}
                and k.ppriceid = #{req.ppid}
            </when>
            <otherwise>
                select ds.toareaid toareaId,
                ds.areaid areaId,
                db.ppriceid ppid,
                ds.send_dtime sendTime,
                isnull(lcount,0)-isnull(InStockCount,0) lcount from diaobo_sub ds with(nolock)
                left join diaobo_basket db with(nolock) on ds.id = db.sub_id
                where db.basket_id is null
                and ds.stats in (2,3,5,6)
                and ds.toareaid = #{req.toareaId}
                and db.ppriceid = #{req.ppid}
            </otherwise>
        </choose>
    </select>
    <select id="getInPriceByPpid" resultType="com.jiuji.oa.oacore.weborder.res.WebStockPriceResVO">
        <choose>
            <when test="req.type == 1">
                select top 2
                k.id mkcId,
                ppriceid ppid,
                areaid areaId,
                a.area,
                staticprice inPrice,
                imeidate imeiDate from product_mkc k with(nolock)
                left join areainfo a with(nolock) on k.areaid = a.id
                where k.kc_check in (2,3)
                and isnull(mouldFlag,0)=0
                and basket_id is null
                and imei is not null
                and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1)
                and k.ppriceid = #{req.ppid}
                and k.areaid = #{req.areaId}
                order by staticprice asc
            </when>
            <when test="req.type == 2">
                select top 1
                k.id mkcId,ppriceid ppid,
                areaid areaId,
                a.area,
                staticprice inPrice,
                imeidate imeiDate from product_mkc k with(nolock)
                left join areainfo a with(nolock) on k.areaid = a.id
                where k.kc_check in (2,3,10)
                and isnull(mouldFlag,0)=0
                and basket_id is null
                and imei is not null
                and not exists(select 1 from dbo.xc_mkc xc with(nolock) where xc.mkc_id=k.id and isnull(xc.isLock,0)=1)
                and k.ppriceid = #{req.ppid}
                <if test="req.authorizeId != null">
                    and a.authorizeid = #{req.authorizeId}
                </if>
                <if test="req.xtenant != null">
                    and a.xtenant = #{req.xtenant}
                </if>
                order by imeidate desc
            </when>
            <otherwise>
                select top 1 ppriceid ppid,
                inprice inPrice
                from product_kc pk with(nolock)
                left join areainfo a with(nolock) on pk.areaid = a.id
                where pk.ppriceid = #{req.ppid}
                <if test="req.authorizeId != null">
                    and a.authorizeid = #{req.authorizeId}
                </if>
                <if test="req.xtenant != null">
                    and a.xtenant = #{req.xtenant}
                </if>
            </otherwise>
        </choose>
    </select>
    <select id="selectPpidByProductId" resultType="java.lang.Integer">
        select ppriceid from dbo.productinfo wiht(nolock ) where product_id = #{productId}
    </select>
    <select id="getGiftStockByAreaIds" resultType="com.jiuji.oa.oacore.weborder.res.GiftStockResVO">
        select k.ppriceid as ppid,leftCount as stockCount,areaid as areaId
        from dbo.product_kc k with(nolock)
        left join productinfo p with(nolock) on k.ppriceid = p.ppriceid
        where p.cid in (select id from f_category_children ('98,343'))
        and k.leftCount > 0
        and k.areaid in
        <foreach collection="req.areaIds" open="(" close=")" item="areaId" index="index" separator=",">
            #{areaId}
        </foreach>
    </select>
    <select id="getProductInPriceByPpid" resultType="com.jiuji.oa.oacore.weborder.res.WebStockPriceResVO">
        select top 1 p.ppriceid ppid,
                p.costprice inPrice
        from productinfo p with(nolock)
        where p.ppriceid = #{req.ppid}
        and p.costprice > 0
    </select>
</mapper>
