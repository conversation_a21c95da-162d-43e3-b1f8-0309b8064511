<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.OrderNoticeMapper">


    <select id="getUndoneOrder" resultType="com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO">
        select top 1 *
        from (
                 SELECT 1                        kind,
                        COUNT(DISTINCT s.sub_id) subNumber
                 FROM dbo.sub s WITH (NOLOCK)
                                  INNER JOIN dbo.basket b
                 WITH (NOLOCK)
                 ON b.sub_id = s.sub_id
                 WHERE s.sub_check IN (0, 1, 2, 6, 5, 7)
                   AND ISNULL(b.isdel
                     , 0) = 0
                   AND b.seller = #{inUser}
                 UNION
                 SELECT 2,
                     COUNT(DISTINCT rmi.sub_id)
                 FROM dbo.recover_marketInfo rmi
                 WITH (NOLOCK)
                     INNER JOIN dbo.recover_marketSubInfo rms
                 WITH (NOLOCK)
                 ON rms.sub_id = rmi.sub_id
                 WHERE ISNULL(rmi.saleType
                     , 0) = 0
                   AND rmi.sub_check IN (0, 1, 2, 6, 5, 7)
                   AND ISNULL(rms.isdel
                     , 0) = 0
                   AND rms.seller = #{inUser}
                 UNION
                 SELECT 3,
                     COUNT(DISTINCT yy.id)
                 FROM dbo.shouhou_yuyue yy
                 WITH (NOLOCK)
                 WHERE yy.stats IN (2, 4)
                   AND ISNULL(yy.isdel
                     , 0) = 0
                   AND ISNULL(yy.iszy
                     , 0) = 0
                   AND ISNULL(yy.check_user
                     , yy.enterUser) = #{inUser}
                 UNION
                 SELECT 6        AS kind,
                     COUNT(1) AS count_
                 FROM dbo.Smallpro s
                 WITH (NOLOCK)
                 WHERE s.Stats IN (0)
                   AND isnull(s.isdel
                     , 0) = 0
                   AND s.Inuser = #{inUser}
                 UNION
                 select 8 as kind, count(distinct b.sub_id)
                 from dbo.recover_basket b
                 with (nolock)
                     inner join dbo.recover_sub s
                 with (nolock)
                 on s.sub_id = b.sub_id
                 where s.sub_check in (1, 2, 5)
                   and ISNULL(b.isdel, 0) = 0
                   and b.inuser = #{inUser}
                 UNION
                 select 8 as kind, count(distinct b.sub_id)
                 from dbo.recover_basket b with (nolock)
                          inner join dbo.recover_sub s with (nolock) on s.sub_id = b.sub_id
                          inner join dbo.recover_sub_extend se with(nolock) on se.sub_id = b.sub_id
                 where s.sub_check = 5 and s.sub_delivery = 2 and b.checkUser =  #{inUser}
                   and ISNULL(b.isdel, 0) = 0
                   and b.checkUser != b.inuser
                   and (s.express_recover_status =2 OR se.signature is not null)
                union
                SELECT 4 AS kind, count(distinct t.id)
                   FROM
                       (
                           SELECT sh.id,so.inuser,so.mark,so.timeoutdate,
                                  row_number() over(partition by so.shouhouid,so.mark order by so.timeoutdate desc) rn
                           FROM dbo.shouhou sh WITH(nolock)
                                    INNER JOIN dbo.shouhou_other so WITH(nolock) ON sh.id = so.shouhouid
                           WHERE isnull(sh.isquji,0) = 0
                             AND isnull(sh.xianshi,0)= 1
                             AND so.kind = 3
                             AND isnull(sh.iszy,0) = 0
                             AND NOT EXISTS ( SELECT 1 FROM dbo.shouhou_qudao sq WITH(nolock)
                                              WHERE sq.shouhouid = sh.id) AND so.inuser = #{inUser}
                             AND (
                                   (so.mark = 0 AND DATEDIFF(HOUR,so.timeoutdate,GETDATE()) &gt;= 72)
                                   OR
                                   (so.mark = 1 AND DATEDIFF(HOUR,so.timeoutdate,GETDATE()) &gt;= 24)
                               )
                       ) t
                   WHERE t.rn = 1

             ) a
        order by subNumber desc
    </select>
    <select id="listUndoneOrder" resultType="com.jiuji.oa.oacore.oaorder.bo.UndoneOrderBo">
        SELECT *
        FROM (<include refid="listUndoneOrderCombinedSql"></include>) a
        ORDER BY sort_order desc,time asc
        OFFSET (#{req.current} - 1) * #{req.size} ROWS FETCH NEXT #{req.size} ROWS ONLY

    </select>
    <select id="countUndoneOrder" resultType="java.lang.Long">
        SELECT count(1) FROM (<include refid="listUndoneOrderCombinedSql"></include>) a
    </select>
    <sql id="listUndoneOrderCombinedSql">
        SELECT *
        FROM (
            SELECT *,
            ROW_NUMBER() OVER (
            PARTITION BY orderType, orderId
            ORDER BY sort_order desc
            ) AS rn
            from (
                <include refid="listUndoneOrderSql"></include>
            ) as bb
        ) AS t
        WHERE rn = 1
    </sql>


    <select id="getGoldOrder" resultType="com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO">
        SELECT
            top 1
            13 AS kind,
            COUNT(1) AS subNumber
        FROM
            huishou_gold_reservation h WITH (NOLOCK)
        where
            h.del_flag = 0
          and h.arrival_flag = 0
          and h.ch999UserName like concat('%,', #{inUser}, ',%')
    </select>

    <select id="getZhuanShouUndoneOrder" resultType="com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO">
        select 9 as kind, count(*) as subNumber FROM dbo.recover_marketInfo rmi WITH (NOLOCK)
        WHERE  rmi.sub_check IN (0, 1, 2, 6, 5, 7)
          AND rmi.sub_to = '内部借用渠道'
          AND exists(select 1 from dbo.recover_marketSubInfo rms WITH (NOLOCK) where rms.sub_id = rmi.sub_id AND ISNULL(rms.isdel, 0) = 0 AND rms.seller = #{inUser})
        union all
        select 10 as kind, count(*) as subNumber FROM dbo.recover_marketInfo rmi WITH (NOLOCK)
        WHERE  rmi.sub_check IN (0, 1, 2, 6, 5, 7)
          AND rmi.sub_to = '内部购买渠道'
          AND exists(select 1 from dbo.recover_marketSubInfo rms WITH (NOLOCK) where rms.sub_id = rmi.sub_id AND ISNULL(rms.isdel, 0) = 0 AND rms.seller = #{inUser})
    </select>

    <select id="getRecoverRedemption" resultType="com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO">
        select distinct 11 as kind, 1 as subNumber, rmi.sub_id as subId
        FROM dbo.recover_marketInfo rmi WITH (NOLOCK)
         inner join recover_marketSubInfo rmSI WITH (NOLOCK) on rmSI.sub_id = rmi.sub_id
            inner join recover_mkc k WITH (NOLOCK) on rmSI.basket_id = k.to_basket_id
            inner join recover_basket b WITH (NOLOCK) on k.from_basket_id = b.id
            inner join recover_sub sub WITH (NOLOCK) on b.sub_id = sub.sub_id
        WHERE
          rmi.sub_to = '回收机退回渠道'
          and sub.areaid = #{areaId}
          and isnull(rmSI.isdel,0)=0
          and ISNULL(b.isdel,0) = 0
          and rmi.sub_check IN (0, 1, 2, 6, 5, 7)
    </select>


    <sql id="listUndoneOrderSql">
        <trim prefixOverrides="UNION ALL">
            <if test="req.userName != null and req.orderTypes.contains(1)">
                <!--销售单-->
                SELECT 1 orderType,cast(s.sub_id as varchar) orderId,s.areaid areaId,s.userid userId,s.sub_to userName,s.sub_mobile mobile,s.sub_date time
                     ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                FROM dbo.sub s WITH (NOLOCK)
                WHERE s.sub_check IN (0, 1, 2, 6, 5, 7)
                AND exists(select 1 from dbo.basket b WITH (NOLOCK) where b.sub_id = s.sub_id AND ISNULL(b.isdel, 0) = 0 and b.seller = #{req.userName})
            </if>
            <if test="req.userName != null and req.orderTypes.contains(2)">
                <!--良品单-->
                UNION ALL
                SELECT 2 orderType,cast(rmi.sub_id as varchar) orderId,rmi.areaid areaId,rmi.userid userId,rmi.sub_to userName,rmi.sub_mobile mobile,rmi.sub_date time
                    ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                FROM dbo.recover_marketInfo rmi WITH (NOLOCK)
                WHERE ISNULL(rmi.saleType, 0) = 0 AND rmi.sub_check IN (0, 1, 2, 6, 5, 7)
                and exists(select 1 from dbo.recover_marketSubInfo rms WITH (NOLOCK)
                where rms.sub_id = rmi.sub_id AND ISNULL(rms.isdel, 0) = 0 AND rms.seller = #{req.userName})
            </if>
            <if test="req.userName != null and req.orderTypes.contains(3)">
                <!--预约单-->
                UNION ALL
                SELECT 3 orderType,cast(yy.id as varchar) orderId,yy.areaid areaId,yy.userid userId,yy.username userName,yy.mobile mobile,yy.dtime time
                    ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                FROM dbo.shouhou_yuyue yy WITH (NOLOCK)
                WHERE yy.stats IN (2, 4) AND ISNULL(yy.isdel, 0) = 0
                AND ISNULL(yy.iszy, 0) = 0
                AND ISNULL(yy.check_user, yy.enterUser) = #{req.userName}
            </if>
            <if test="req.userName != null and req.orderTypes.contains(6)">
                <!--售后小件单-->
                UNION ALL
                SELECT 6 orderType,cast(s.id as varchar) orderId,isnull(s.toareaid,s.areaid) areaId,s.userid userId,s.Username userName,s.mobile mobile,s.indate time
                     ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                FROM dbo.Smallpro s
                WITH (NOLOCK)
                WHERE s.Stats IN (0)
                AND isnull(s.isdel, 0) = 0
                AND s.Inuser = #{req.userName}
            </if>
            <if test="req.userName != null and req.orderTypes.contains(8)">
                <!--良品单-->
                UNION ALL
                select 8 orderType,cast(s.sub_id as varchar) orderId,s.areaid areaId,s.userid userId,s.sub_to userName,s.sub_tel mobile,s.dtime time
                     ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                from dbo.recover_sub s with(nolock)
                where s.sub_check in (1, 2, 5)
                and exists(select 1 from dbo.recover_basket b with (nolock)
                where s.sub_id = b.sub_id and ISNULL(b.isdel, 0) = 0 and b.inuser = #{req.userName})
                UNION All
                select 8 orderType,cast(s.sub_id as varchar) orderId,s.areaid areaId,s.userid userId,s.sub_to userName,s.sub_tel mobile,s.dtime time
                    ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                from dbo.recover_sub s with (nolock)
                inner join dbo.recover_sub_extend se with(nolock) on se.sub_id = s.sub_id
                where s.sub_check = 5 and s.sub_delivery = 2
                and exists(select 1 from dbo.recover_basket b with (nolock)
                where s.sub_id = b.sub_id and b.checkUser =  #{req.userName} and ISNULL(b.isdel, 0) = 0 and b.checkUser != b.inuser)
                and (s.express_recover_status =2 OR se.signature is not null)
            </if>
            <if test="req.userName != null and req.orderTypes.contains(4)">
                <!--维修单-->
                UNION ALL
                SELECT 4 orderType,cast(t.id as varchar) orderId,t.areaId areaId,t.userid userId,t.userName userName,t.mobile mobile,t.modidate time
                    ,null inAreaId,null outAreaId,null orderMark,null title,1 sort_order
                FROM
                (
                    SELECT sh.id,so.mark,so.timeoutdate,isnull(sh.toareaid,sh.areaid) areaId,sh.mobile mobile,sh.userid,sh.username userName,
                    sh.modidate, row_number() over(partition by sh.id,so.mark order by so.timeoutdate desc) rn
                    FROM dbo.shouhou sh WITH(nolock)
                    INNER JOIN dbo.shouhou_other so WITH(nolock) ON sh.id = so.shouhouid
                    WHERE isnull(sh.isquji,0) = 0
                    AND isnull(sh.xianshi,0)= 1
                    AND so.kind = 3
                    AND isnull(sh.iszy,0) = 0
                    AND NOT EXISTS ( SELECT 1 FROM dbo.shouhou_qudao sq WITH(nolock) WHERE sq.shouhouid = sh.id)
                    AND so.inuser = #{req.userName}
                    AND (
                        (so.mark = 0 AND DATEDIFF(HOUR,so.timeoutdate,GETDATE()) &gt;= 72)
                        OR
                        (so.mark = 1 AND DATEDIFF(HOUR,so.timeoutdate,GETDATE()) &gt;= 24)
                    )
                ) t
                WHERE t.rn = 1
            </if>
            <if test="req.orderTypes.contains(5)">
                <!--暂不知道实现方法-->
            </if>
            <if test="req.areaId !=null and req.orderTypes.contains(9)">
                UNION ALL
                <!--大件调拨-->
                select  9 orderType,k.orderid orderId,m.areaid areaId
                        <!-- 调拨没有会员信息 此处用来作为 transerType的变量 -->
                       ,case when isnull(be.stock_method,0)=10 then 2 when isnull(be.stock_method_kind,0)=1 then 1 else 0 end userId
                       ,null userName,null mobile,m.[dtime] time
                     ,m.toareaid inAreaId,m.areaid outAreaId,
                       case when isnull(be.stock_method,0)=10 then '(加急调货-跑腿)' when isnull(m.transfer_label,0)=1 then '(同城调拨-跑腿)' when isnull(be.stock_method_kind,0)=1 then '(风险订单-定时)' else '' end orderMark,null title,
                   case when isnull(be.stock_method,0)=10 then 5
                        when isnull(m.transfer_label,0)=1 then 4
                        when isnull(be.stock_method_kind,0)=1 then 2
                   else 1 end sort_order
                from mkc_toarea m with(nolock)
                left join product_mkc k with(nolock) on m.mkc_id=k.id
                left join dbo.basket_extend as be with(nolock) on k.basket_id=be.basket_id
                where k.id is not null and k.kc_check &lt;&gt; 4
                <!--订购的订单-->
                and k.basket_id is not null
                <!--0 未发出 1 准备发货-->
                and m.stats in(0,1)
                and k.kc_check = 10
                <!--发货门店id-->
                and m.areaid = #{req.areaId}
                and m.id not in (<include refid="machineHandleSql"></include> and pso.order_type in(4,8))
                UNION ALL
                select 9 orderType,k.orderid orderId,m.areaid areaId,
                2 userId,
                null userName,null mobile,m.[dtime] time,
                m.toareaid inAreaId,m.areaid outAreaId,
                '（跑腿调货）' orderMark,
                null title,
                3 sort_order
                from mkc_toarea m with(nolock)
                left join product_mkc k with(nolock) on m.mkc_id=k.id
                left join wuliu w with(nolock) on w.id=m.wuliuid
                where m.stats in (0)
                and m.areaid = #{req.areaId}
                and w.wutype = 1
                and isnull(w.nu,'') != ''
                and w.com in ('meituan','uupt','dada','sftc','shansong')
            </if>
            <if test="req.areaId !=null and req.orderTypes.contains(10)">
                UNION ALL
                <!--小件调拨-->
                select 10 orderType,cast(s.id as varchar) orderId,s.areaid areaId,null userId,null userName,null mobile,s.check_dtime time
                ,s.toareaid inAreaId,s.areaid outAreaId,
                case when isnull(be.stock_method,0)=10 then '(加急调货-跑腿)' when isnull(s.jiaji,0)=4 then '(同城调拨-跑腿)' when isnull(be.stock_method_kind,0)=1 then '(风险订单-定时)' else '' end orderMark,s.title title,
                case when isnull(be.stock_method,0)=10 then 4
                     when isnull(s.jiaji,0)=4 then 5
                     when isnull(be.stock_method_kind,0)=1 then 2
                     else 1 end sort_order
                from diaobo_sub s with(nolock)
                left join diaobo_basket dbb with(nolock)  on s.id=dbb.sub_id
                left join dbo.basket_extend as be with(nolock) on dbb.basket_id=be.basket_id and dbb.basket_type = 0
                left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
                where 1 = 1 and ISNULL(b.type,0) &lt;&gt; 22 and (s.diaoboType not in (2,4) or s.diaoboType is null)
                and s.kinds in('pj')
                and dbb.basket_id is not null
                <!--2 已审核 5 准备发货-->
                and s.stats in (2,5)
                and s.areaid = #{req.areaId}
                and s.id not in (<include refid="machineHandleSql"></include> and pso.order_type in(2,9))
                UNION ALL
                select 10 orderType,
                cast(s.id as varchar) orderId,
                s.areaid areaId,
                null userId,
                null userName,
                null mobile,
                s.check_dtime time,
                s.toareaid inAreaId,
                s.areaid outAreaId,
                '（跑腿调货）' orderMark,
                s.title title,
                2 sort_order
                from diaobo_sub s with(nolock)
                left join wuliu w with(nolock) on w.id=s.wuliuid
                where s.kinds in('pj')
                and (s.diaoboType not in (2,4) or s.diaoboType is null)
                and s.stats in (2)
                and s.areaid = #{req.areaId}
                and w.wutype = 1
                and isnull(w.nu,'') != ''
                and w.com in ('meituan','uupt','dada','sftc','shansong')
            </if>
            <if test="req.areaId !=null and req.orderTypes.contains(11)">
                union all
                <!--良品调拨-->
                select 11 orderType,k.orderid orderId,m.areaid areaId,
                case when isnull(m.transfer_label,0)=1 then 2
                    else 0 end userId,
                null userName,null mobile,m.[dtime] time
                    ,m.toareaid inAreaId,m.areaid outAreaId,
                case when isnull(m.transfer_label,0)=1 then '(同城调拨-跑腿)' else '' end orderMark,
                null title,
                case when isnull(m.transfer_label,0)=1 then 3
                    when w.wutype = 1 and isnull(w.nu,'') != '' and w.com in ('meituan','uupt','dada','sftc','shansong') then 2
                    else 1 end sort_order
                FROM recover_toarea m with(nolock)
                LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
                left join wuliu w with(nolock) on w.id=m.wuliuid
                WHERE k.id is not NULL
                <!--良品库存-->
                and ISNULL(k.issalf,0) =1
                <!--订购的订单-->
                and k.from_basket_id is not null
                <!--0 未发出 1 准备发货-->
                and m.status in (0,1)
                <!--发货门店id-->
                and m.areaid = #{req.areaId}
                and k.id not in (<include refid="machineHandleSql"></include> and pso.order_type = 1)
                union all
                select 11 orderType,
                k.orderid orderId,
                m.areaid areaId,
                2 userId,
                null userName,
                null mobile,
                m.[dtime] time,
                m.toareaid inAreaId,
                m.areaid outAreaId,
                '（跑腿调货）' orderMark,
                null title,2 sort_order
                FROM recover_toarea m with(nolock)
                LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
                left join wuliu w with(nolock) on w.id=m.wuliuid
                WHERE k.id is not NULL
                and ISNULL(k.issalf,0) =1
                and m.status in (0)
                and m.areaid = #{req.areaId}
                and w.wutype = 1
                and isnull(w.nu,'') != ''
                and w.com in ('meituan','uupt','dada','sftc','shansong')
            </if>
            <if test="req.areaId !=null and req.orderTypes.contains(12)">
                union all
                <!--小件优品调拨-->
                select 12 orderType,cast(s.id as varchar) orderId,s.areaid areaId,null userId,null userName,null mobile,s.check_dtime time
                    ,s.toareaid inAreaId,s.areaid outAreaId,null orderMark,s.title title,1 sort_order
                from diaobo_sub s with(nolock)
                left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
                left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
                where 1 = 1 and ISNULL(b.type,0) = 22
                and s.kinds in('pj')
                and dbb.basket_id is not null
                <!--2 已审核 5 准备发货-->
                and s.stats in (2,5)
                and s.areaid = #{req.areaId}
                union all
                <!--小件优品调拨-->
                select 12 orderType,cast(s.id as varchar) orderId,s.areaid areaId,null userId,null userName,null mobile,s.check_dtime time
                    ,s.toareaid inAreaId,s.areaid outAreaId,null orderMark,s.title title,1 sort_order
                from diaobo_sub s with(nolock)
                left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
                left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
                where 1 = 1 and s.diaoboType in (2,4)
                and s.kinds in('pj')
                and dbb.basket_id is not null
                <!--2 已审核 5 准备发货-->
                and s.stats in (2,5)
                and s.areaid = #{req.areaId}
                union all
                select 12 orderType,
                cast(s.id as varchar) orderId,
                s.areaid areaId,
                null userId,
                null userName,
                null mobile,
                s.check_dtime time,
                s.toareaid inAreaId,
                s.areaid outAreaId,
                '（跑腿调货）' orderMark,
                s.title title,
                2 sort_order
                from diaobo_sub s with(nolock)
                left join wuliu w with(nolock) on w.id=s.wuliuid
                where s.kinds in('pj')
                and s.stats in (2)
                and s.diaoboType in (2,4)
                and s.areaid = #{req.areaId}
                and w.wutype = 1
                and isnull(w.nu,'') != ''
                and w.com in ('meituan','uupt','dada','sftc','shansong')
            </if>
            <if test="req.areaId !=null and req.orderTypes.contains(13)">
                union all
                <!--维修配件调拨-->
                select 13 orderType,cast(s.id as varchar) orderId,s.areaid areaId,null userId,null userName,null mobile,s.check_dtime time
                    ,s.toareaid inAreaId,s.areaid outAreaId,
                case when isnull(s.jiaji,0)=4 then '(同城调拨-跑腿)' else '' end orderMark,
                s.title title,1 sort_order
                from diaobo_sub s with(nolock)
                left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
                where 1 = 1
                and s.kinds in('wx')
                and dbb.basket_id is not null
                <!--2 已审核 5 准备发货-->
                and s.stats in (2,5)
                and s.areaid = #{req.areaId}
                and s.id not in (<include refid="machineHandleSql"></include> and pso.order_type in(3,10))
                union all
                select 13 orderType,
                cast(s.id as varchar) orderId,
                s.areaid areaId,
                null userId,
                null userName,
                null mobile,
                s.check_dtime time,
                s.toareaid inAreaId,
                s.areaid outAreaId,
                '（跑腿调货）' orderMark,
                s.title title,
                2 sort_order
                from diaobo_sub s with(nolock)
                left join wuliu w with(nolock) on w.id=s.wuliuid
                where s.kinds in('wx')
                and s.stats in (2)
                and s.areaid = #{req.areaId}
                and w.wutype = 1
                and isnull(w.nu,'') != ''
                and w.com in ('meituan','uupt','dada','sftc','shansong')
            </if>
        </trim>
    </sql>
    <sql id="machineHandleSql">
        SELECT pso.related_id from product_stock_out pso with (nolock)
                                where order_status not in (1,6) and is_delete = 0
    </sql>
</mapper>
