package com.jiuji.oa.oacore.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum  EvaluateTagTypeEnum implements CodeMessageEnumInterface {
    /**
     * 有效：非 失效/城市经理审核/申诉中的评价为有效评价
     */
    Valid(0,"有效"),
    // Positive(1, "正面"),
    // Negative(2, "负面"),
    // Suggest(3, "建议"),
    Invalid(6, "失效"),
    <PERSON><PERSON>(7, "申诉中"),
    // Dianxing(8, "典型案例"),
    // NotEvaluate(9, "未评估"),
    CityManagerCheck(10, "城市经理审核");
    private Integer code;
    private String message;

    public static String getMessageByCode(Integer code) {
        for (EvaluateTagTypeEnum evaluateTagTypeEnum : EvaluateTagTypeEnum.class.getEnumConstants()) {
            if (Objects.equals(evaluateTagTypeEnum.getCode(), code)) {
                return evaluateTagTypeEnum.getMessage();
            }
        }
        return "";
    }
}
