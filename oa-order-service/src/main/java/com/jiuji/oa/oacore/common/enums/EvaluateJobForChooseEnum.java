package com.jiuji.oa.oacore.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/1/25
 */
@Getter
@AllArgsConstructor
public enum EvaluateJobForChooseEnum implements CodeMessageEnumInterface {

    XIAOSHOU(1, "导购"),
    YANJI(4, "验机"),
    JISHUSERVICE(13, "技术服务"),
    RECYCLER(21, "回收-加单"),
    SHOUHOUKEFU(7, "技术客服"),
    WEIXIUGONGCHENGSHI(8, "维修"),
    SMALLPROCONNECTOR(20, "小件售后-接件"),
    SCREENPROTECTORCONNECTOR(23, "贴膜售后-接件"),
    WULIUPEISONG(6, "配送"),
    LIANGPINCHOOSE(22, "良品-选机"),
    LIANGPINCHECK(27, "良品-验机"),
    ONLINESERICES(15, "在线客服"),
    CALLSERVICE(14, "呼叫服务"),
    FOLLOWUPPERSON(26, "投诉-跟进人"),
    DEALPERSON(28, "投诉-处理人"),
    ;


    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;

    public static String getEvaluateJobMessage(Integer code) {
        for(EvaluateJobForChooseEnum temp : EvaluateJobForChooseEnum.values()) {
            if (temp.getCode().equals(code)) {
                return temp.getMessage();
            }
        }
        return "";
    }

}
