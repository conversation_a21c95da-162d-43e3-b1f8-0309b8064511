package com.jiuji.oa.oacore.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * description: <订单服务返回结果的结构>
 * translation: <The structure of the result returned by the order service>
 *
 * <AUTHOR>
 * @date 2019/11/11
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderServiceResult implements Serializable {

    private static final long serialVersionUID = 2309929550417502840L;
    /**
     * 返回结果状态
     */
    private Integer code;
    /**
     * 返回结果信息
     */
    private String message;
    /**
     * 返回结果数据
     */
    private Object data;

    public static OrderServiceResult success(Object data, String message) {
        return new OrderServiceResult(0, message, data);
    }
}
