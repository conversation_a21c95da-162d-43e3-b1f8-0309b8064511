package com.jiuji.oa.oacore.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ERecoverStateEnum implements CodeMessageEnumInterface {

    UNCONFIRM(0, "未确认"),
    CONFIRMED(1, "已确认"),
    AUDITED(5, "已审核"),
    COMPLETED(3, "已完成"),
    DELETED(4, "删除"),
    STORAGED(2, "已入库"),
    REFUND(9, "退款");

    private Integer code;
    private String message;
}
